-- Setup script for the Hello Snowflake! app.

CREATE APPLICATION ROLE IF NOT EXISTS app_public;
CREATE SCHEMA IF NOT EXISTS core;
GRANT USAGE ON SCHEMA core TO APPLICATION ROLE app_public;

CREATE OR REPLACE PROCEDURE CORE.HELLO()
  <PERSON><PERSON><PERSON>NS STRING
  LANGUAGE SQL
  EXECUTE AS OWNER
  AS
  BEGIN
    RETURN 'Hello Snowflake!';
  END;

GRANT USAGE ON PROCEDURE core.hello() TO APPLICATION ROLE app_public;

CREATE OR ALTER VERSIONED SCHEMA code_schema;
GRANT USAGE ON SCHEMA code_schema TO APPLICATION ROLE app_public;

CREATE VIEW IF NOT EXISTS code_schema.accounts_view
  AS SELECT NAME
  FROM shared_data.accounts;

CREATE VIEW IF NOT EXISTS code_schema.bsm_view_all
  AS SELECT *
  FROM SHARED_DATA_FOR_BSM.ALL_BSM_DATA_PREPROCCESSED;

CREATE VIEW IF NOT EXISTS code_schema.digi_merge_view
  AS SELECT *
  FROM SHARED_DATA_FOR_BSM.DIGI_MERGED;

GRANT SELECT ON VIEW code_schema.accounts_view TO APPLICATION ROLE app_public;
-- GRANT SELECT ON VIEW code_schema.bsm_view_all TO APPLICATION ROLE app_public;


CREATE STREAMLIT IF NOT EXISTS code_schema.hello_snowflake_streamlit
  FROM '/streamlit'
  MAIN_FILE = '/app.py';

GRANT USAGE ON STREAMLIT code_schema.hello_snowflake_streamlit TO APPLICATION ROLE app_public;