import textdistance
import pandas as pd
import streamlit as st
from datetime import date

def build_match_conditions(name, column_name, reference_column):
    if name:
        return f"""
            CASE
                WHEN '{name}' != '' AND 
                    {' OR '.join([f"{reference_column} ILIKE '%' || '{part}' || '%'" for part in name.split()])}
                THEN 1.0
                WHEN '{name}' != '' AND 
                    {' OR '.join([f"{column_name}_SOUNDEX = SOUNDEX('{part}')" for part in name.split()])}
                THEN 0.8
                ELSE 0
            END
        """
    return '0'

def mask_value(value):
    if isinstance(value, str) and value:
        return value[0] + '*' * (len(value) - 1)
    elif isinstance(value, date) and value:
        value_str = value.strftime('%Y-%m-%d')
        return value_str[0] + '*' * (len(value_str) - 1)
    return value
#--------------------------------------------------------------------------------------------------------------------#
#                                                  Name Matching Functions
#--------------------------------------------------------------------------------------------------------------------#

class Name:
    def __init__(self, name):
        self.name = name.lower()  # Convert to lowercase
    
    def exact(self, other_name):
        """
        Checks if the current name exactly matches the other name.
        Both names must be more than one character long.
        """
        other_name = other_name.lower() if other_name else ""
        # if (len(self.name) > 1 and len(other_name) > 1 and self.name == other_name):
        if (self.name == "" and other_name == "") or (len(self.name) > 1 and len(other_name) > 1 and self.name == other_name):
            return 'Exact Match'
        if not self.name or not other_name:  # Check for empty strings
            return False
        return False
    
    def hyphenated(self, other_name):
        """
        Checks if the current name matches a hyphenated version of the other name.
        Example: 'Smith' matches 'Smith-Jones'
        """
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        if '-' in other_name and self.name in other_name.split('-'):
            return "Hyphenated Name"
        return False

    def fuzzy(self, other_name, threshold=0.85):
        """
        Checks if the current name matches the other name based on fuzzy matching.
        The threshold determines the minimum similarity score required to consider it a match.
        Example: 'Brown' matches 'Browne'
        """
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        # similarity = fuzz.ratio(self.name, other_name)
        similarity = textdistance.jaro_winkler(self.name, other_name)
        if similarity >= threshold:
            return f"Fuzzy Match with similarity score: {similarity}"
        return False

    def nickname(self, other_name):
        """
        Checks if the current name matches a nickname or an alternative spelling of the other name.
        Example: 'Bob' matches 'Robert'
        """
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        nicknames_database = {
            "bob": ["robert"],
            "roberto": ["robert"],
            "enrique": ["henry", "hank"],
            "john": ["j"]
            # Add more nicknames and variations here
        }
        
        # Look up nicknames in the database
        if self.name in nicknames_database:
            if other_name in nicknames_database[self.name]:
                return "Nickname Match"
        return False

    def initial(self, other_name):
        """
        Checks if the current name is an initial of the other name or vice versa.
        Example: 'John' matches 'J'
        """
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        # if len(self.name) == 1 and other_name.startswith(self.name):
        if other_name.startswith(self.name[0]):
            return "Initial Match"
        if len(other_name) == 1 and self.name.startswith(other_name):
            return "Initial Match"
        return False

    def transposed(self, other_name):
        """
        Checks if the components of the names are transposed.
        Example: 'John Robert Smith' matches 'Robert John Smith'
        """
        self_parts = self.name.split()
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        other_parts = other_name.split()
        
        if len(self_parts) == len(other_parts) and sorted(self_parts) == sorted(other_parts):
            return "Transposed Match"
        return False

    def missing(self, other_name):
        """
        Checks if the current name is a subset of the other name or vice versa.
        Example: 'John Robert Smith' matches 'John Smith'
        """
        if not self.name:
            return "Self name is empty"
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        if self.name in other_name or other_name in self.name:
            return "Missing Part Match"
        return False

    def different(self, other_name):
        """
        Checks if there is no match between the names.
        Example: 'John' matches 'Robert'
        """
        other_name = other_name.lower() if other_name else ""  # Handle empty input
        if not self.name or not other_name:  # Check for empty strings
            return False
        if self.name != other_name:
            return "Different Name"
        return False
    
def apply_name_matching(row, name_Str, db_column, input_field, str_index):
    name_obj = Name(input_field)  # Initialize Name object with the input_field

    # Iterate through all match types
    for match_type, replacement_char in name_match_actions.items():
        # Dynamically call the corresponding method (exact, nickname, etc.)
        if getattr(name_obj, match_type)(row[db_column]):
            # Replace the character at str_index in name_Str with the replacement_char
            name_Str = name_Str[:str_index] + replacement_char + name_Str[str_index+1:]
            break  # Stop after the first match is found

    return name_Str

name_match_actions = {
    'exact': 'E',
    'nickname': 'N',
    'hyphenated': 'H',
    'fuzzy': 'F',
    'initial': 'I',
    'transposed': 'T',
    'missing': 'M',
    'different': 'D'
}

#--------------------------------------------------------------------------------------------------------------------#


#--------------------------------------------------------------------------------------------------------------------#
#                                                  DOB Matching Functions
#--------------------------------------------------------------------------------------------------------------------#
dob_match_actions = {
    'exact': 'E'}

class Dob:
    def __init__(self, dob):
        self.dob = dob  # Initialize with dob
    
    def exact(self, other_dob):
        """
        Checks if the current dob exactly matches the other dob.
        """
        # Convert both dobs to strings for comparison
        if str(self.dob) == str(other_dob):
            return 'Exact Match'
        return 'No Match'
#--------------------------------------------------------------------------------------------------------------------#


#--------------------------------------------------------------------------------------------------------------------#
#                                                  Matching Level Functions
#--------------------------------------------------------------------------------------------------------------------#
def get_matching_level(df,dob,mobile,email,name_matching_score,address_matching_weights):

    levels = []
    name_score_levels = {
        (97, 100): 'FullName',
        (75, 97): 'PartialName'}

    address_score_levels = {
        (91, 100): 'FullAddress',
        (79, 90): 'PartialAddress'}

    for score_range, level in name_score_levels.items():
        if score_range[0] <= name_matching_score <= score_range[1]:
            levels.append(f'{level} - {int(name_matching_score)}'+"%")

    for score_range, level in address_score_levels.items():
        if score_range[0] <= address_matching_weights <= score_range[1]:
            levels.append(f'{level} - {int(address_matching_weights)}'+"%")
    if 'DOB' in df.columns and pd.notna(df['DOB'].iloc[0]) and str(df['DOB'].iloc[0]) == dob:
        levels.append('DOB')
    if 'MOBILE' in df.columns and pd.notna(df['MOBILE'].iloc[0]) and mobile !='' and str((df['MOBILE'].iloc[0])) == (mobile):
        levels.append('Mobile')
    if 'EMAIL' in df.columns and (df['EMAIL'][0] is not None and (df['EMAIL'][0] in df['EMAIL'][0] and df['EMAIL'][0] != "") and df['EMAIL'][0] == email):
        levels.append('Email')
    return levels

def append_based_on_verification(result, verified_by =False):
    verified_by = result['Overall Matching Level'][0]
    name_terms = ["FullName", "PartialName"]
    address_terms = ["FullAddress", "PartialAddress"]
    dob_term = "DOB"

    name_check = any(term in verified_by for term in name_terms)
    address_check = any(term in verified_by for term in address_terms)
    dob_check = dob_term in verified_by

    if name_check and address_check and dob_check:
        if verified_by:
            return "M1"
    elif name_check and address_check:
        if verified_by:
            return "N1"
    elif name_check and dob_check:
        if verified_by:
            return "M2"
    return "No Match"

def get_mobile_email_matching_level(df,dob,mobile,email,name_matching_score,address_matching_weights):

    levels = []
    # Define the score ranges and their corresponding levels
    name_score_levels = {
        (97, 100): 'FullName',
        (75, 97): 'PartialName',
    }

    # Check name matching level
    for score_range, level in name_score_levels.items():
        if score_range[0] <= name_matching_score <= score_range[1]:
            levels.append(f'{level} - {int(name_matching_score)}'+"%")

    # if 'Phone2_Mobile' in df.columns and pd.notna(df['Phone2_Mobile'].iloc[0]) and mobile !='' and str(df['Phone2_Mobile'].iloc[0]) == (mobile):
    #     levels.append('Mobile')
    # if (df['EMAILADDRESS'][0] is not None and (df['EMAILADDRESS'][0] in df['EMAILADDRESS'][0] and df['EMAILADDRESS'][0] != "") and df['EMAILADDRESS'][0] == email):
    #     levels.append('Email')
    # if 'MOBILE' in df.columns and pd.notna(df['MOBILE'].iloc[0]) and mobile !='' and str(int(df['MOBILE'].iloc[0])) == (mobile):
    if 'MOBILE' in df.columns and (df['MOBILE'].iloc[0] !="") and mobile !='' and str(int(df['MOBILE'].iloc[0])) == (mobile):
        levels.append('Mobile')
    if 'EMAIL' in df.columns and (df['EMAIL'][0] is not None and (df['EMAIL'][0] in df['EMAIL'][0] and df['EMAIL'][0] != "") and df['EMAIL'][0] == email):
        levels.append('Email')
    return levels

def append_mobile_email_verification(result, verified_by =False):
    # verified_by = result['Overall Matching Level1'][0]
    verified_by = result['Overall Matching Level'][0]

    name_terms = ["FullName", "PartialName"]
    mobile_term = "Mobile"
    email_term = "Email"


    name_check = any(term in verified_by for term in name_terms)
    mobile_check = mobile_term in verified_by
    email_check = email_term in verified_by

    if name_check and mobile_check and email_check:
        # indexes['Sources'][index].append('M1')
        if verified_by:
            return "P1"
    elif name_check and mobile_check:
        # indexes['Sources'][index].append('N1')
        if verified_by:
            return "P2"
    elif name_check and email_check:
        # indexes['Sources'][index].append('M2')
        if verified_by:
            return "P3"
    return "No Match"


def display_match_explanation_new():
    """
    Displays an expander in the Streamlit app with bullet points explaining key concepts of the matching process.
    """
    with st.expander(":red[**Detailed Description**]"):
        st.markdown("""
        :green[**1) IDV Verified Level**] : A multi-attribute determination of verification status.
        - Consolidates matches across **Name**, **Address**, and **Date of Birth (DOB)** into a single outcome.
        """)

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- **M1**: Full Name, Full Address, and DOB Match")
            st.markdown("- **N1**: Full Name and Full Address Match")
        with col2:
            st.markdown("- **M2**: Full Name and DOB Match")

        st.markdown("""
        Verification Level Hierarchy: The verification levels are prioritized as : **M1 > N1 > M2**
        """)


        st.markdown("""
         :green[**2) IDV Contact Verified Level**] : This process determines contact verification by evaluating multiple attributes.  
        - Matches across **Name**, **Mobile**, and **Email** are combined into a unified verification outcome.
        """)

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- **P1**: Match on Full Name, Mobile, and Email")
            st.markdown("- **P2**: Match on Full Name and Mobile")
        with col2:
            st.markdown("- **P3**: Match on Full Name and Email")

        st.markdown("""
         Verification Level Priority: The verification levels are ranked by reliability as follows: **P1**  > **P2** > **P3**
        """)

        st.markdown("""
         :green[**3) IDV Record Verified**] : A validation indicator determined by Single source.
        - **True**: The record is verified and meets the required validation criteria.  
        - **False**: The record is not verified and fails to meet the validation standards.  
        """)

        st.markdown("""
         :green[**4) IDV Multi Level Verification**] : An advanced validation indicator determined by multiple sources.
        - **True**: The record is validated by meeting one or more multi-condition criteria, ensuring higher reliability.  
        - **False**: The record does not satisfy the multi-condition criteria, indicating insufficient validation from multiple sources.  

        A record is marked as **True** if any of the following conditions are met:  """)
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- **M1** >= 2")
            st.markdown("- **M1** >= 1 **and** **M2** >= 1")
        with col2:
            st.markdown("- **M1** >= 1 **and** **N1** >= 1")
            st.markdown("- **M2** >= 1 **and** **N1** >=1 ")
        
         
          
         

        st.markdown("""
         :green[**5) Profile**] : This section provides the database records that align with the input data.  
        - Returns data entries from the database that match the provided input criteria.  
        """)

        '''
        st.markdown("""
        :green[**1) Verification Criteria**]
        1. **Full Name**: Verified against all the name components (FirstName, MiddleName, SurName).
        2. **Full Address**: Verified against all the address components (Address line 1, Suburb, State, Postcode)
        3. **Date of Birth (DOB)**: Validated for Exact DOB.
        4. **Mobile Number**: Verified for exact Contact Number.
        5. **Email Address**: Verified for exact email address.
        """, unsafe_allow_html=True)

        st.markdown("""
        - :green[**True**]: Indicates that the verification is successful and all fields match.
        """, unsafe_allow_html=True)


        st.markdown("""
        :green[**2) Overall Matching Levels**]
        1. **Name Matching**: The system compared all the input names to the reference data and achieved a match score.
        2. **Address Matching**: The input all address components compared with the system address components with a match score.
        3. **DOB Matching**: The provided DOB was compared with system DOB with an accuracy.
        """, unsafe_allow_html=True)

        st.markdown("""
        - :blue[**Full Matching**]: Indicate that the input data closely aligns with the reference data.
        - :orange[**Partial Matching**]: Suggests some discrepancies or variations in specific fields.
        - :red[**No Matching**]: No any similarity.
                    
        """, unsafe_allow_html=True)
        '''

        st.markdown("""
        :green[**6) Scoring**] : A score quantifying how closely each input matches the expected values.""")
        col1, col2 = st.columns(2)
        st.markdown("- (60-99) indicates a fuzzy match.")
        with col1:
            st.markdown("- 100 indicates an exact match.")
        with col2:
            st.markdown("- 0 indicates no match. ")

        st.markdown(""":green[**6) Full Name Similarity**] : Measures how well the full input name matches the target name.""")


        st.markdown(""":green[**7) Name Match Level**] : Categorizes overall name matching.""")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- Exact Match")
            st.markdown("- Middle Name Mismatch")
            st.markdown("- SurName Only Match")
        with col2:
            st.markdown("- Initial Match")
            st.markdown("- Hyphenated Match")
            st.markdown("- Transposed Match")
        
        # st.markdown(""":green[**4) Full Name Similarity**]: Measures how well the full input name matches the target name.""")
        # st.markdown(""":green[**8) Name Match Level**]: Categorized into:""")
        # col1, col2 = st.columns(2)
        # with col1:
        #     st.markdown("- Full Match (90%-97%)")
        # with col2:
        #     st.markdown("- Partial Match (90%-97%)")
        # st.markdown("- No Match (<90%)")

        st.markdown(""":green[**8) DOB Match**] : Indicates whether the date of birth matches.""")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- **True** - Exact Match")
        with col2:
            st.markdown("- **False** - No Match")

        # st.markdown("""
        # :green[**6) Address Matching String**]: Represents the overall match of address components:
        #     - Explain matches for unit number, street name, locality, and postcode:""")
        # col1, col2 = st.columns(2)
        # with col1:
        #     st.markdown("- **A**: Address Line 1")
        #     st.markdown("- **E**: Exact Match")
        #     st.markdown("- **M**: Missing Unit Number")
        #     st.markdown("- **R**: Street Number Range")
        #     st.markdown("- **F**: Partial Street Name")
        # with col2:
        #     st.markdown("- **P**: Postcode")
        #     st.markdown("- **X**: Missing Component")
        #     st.markdown("- **Z**: Different Address")
        #     st.markdown("- **B**: Both Locality & Postcode")
        #     st.markdown("- **L**: Locality")               

        st.markdown("""
                    :green[**9) Address Components Similarities**] : Individual similarity scores for address elements.
            - 100 indicates exact matches for each component.""")
        
        st.markdown("""
        :green[**10) Address Match Level**] : Categorized into.""")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("- Full Match (90%-100%)")
            st.markdown("- Partial Match (70%-90%)")
        with col2:
            st.markdown("- No Match (<70%)")

        # st.markdown("""
        # :green[**9) Overall Matching Level**]: Confidence levels for multi-attribute verification:
        #     - Includes Full Name, Full Address, and Date of Birth percentages.""")

        # :green[**10) Overall Verified Level**]: Final determination of verification status:
        #     - Combines matches across Name, Address, and DOB into a consolidated outcome.
        # """)

        # col1, col2 = st.columns(2)
        # with col1:
        #     st.markdown("- M1 : Full Name Full Address DOB Match")
        #     st.markdown("- N1 : Full Name Full Address Match")
        # with col2:
        #     st.markdown("- M2 : Full Name DOB Match")


IDV_Verified_Level = {
    "M1": "Full Name Full Address DOB Match",
    "N1": "Full Name Full Address Match",
    "M2": "Full Name DOB Match"
}

country_sources = {
    'au' : {
        # 'db_path': "artifacts\\au.db"
        'db_path': "australia",
        'first_name': 'Jila',
        "middle_name" : 'Fakour',
        "sur_name" : 'Tahmasebi',
        "dob" : '1958-07-05',
        "address_line1" : "4 Melissa St",
        "suburb" : "DUNCRAIG",
        "state" : "WA",
        "postcode" : "6023",
        "mobile" : '*********',
        "email" : "<EMAIL>"
            },
    'nz' : {
        # 'db_path': "artifacts\\au.db"
        'db_path': "nz.db",
        'first_name': 'Andrew',
        "middle_name" : 'Joseph',
        "sur_name" : 'Bergersen',
        "dob" : '1992-09-11',
        "address_line1" : "97 Bruce Avenue",
        "suburb" : "Glenview",
        "state" : "Hamilton",
        "postcode" : "3206",
        "mobile" : '*********',
        "email" : "<EMAIL>"
            },
    # 'mx' : {
    #     'db_path': "mx.db",
    #     # 'table_name': "MEXICO_ALL_RECORDS",
    #     "table_name": ['mexico_1M_records','mexico_all_records'],
    #     'first_name':'MARIA',
    #     "middle_name" : 'EUGENIA HERNANDEZ',
    #     'sur_name':'SEGOVIA',
    #     'dob':'2002-12-22',
    #     # 'address_line1':'Monterrey Nuevo León 64930.0',
    #     'addressElement1 (ad1)': 'Monterrey ',
    #     'addressElement2 (sub_district)': 'Nuevo',
    #     'addressElement3 (regency)': 'León',
    #     'addressElement4 (province)': '',
    #     "mobile" : '8117833124',
    #     "email" : ""
    #         },
    'mx':{
        'db_path': "mexico",
        "id_number": "VAUK710226HSLLRY04",
        # 'table_name': "MEXICO_ALL_RECORDS",
        "table_name": ['mexico_1M_records','mexico_all_records'],
        'first_name':'KEYDI',
        "middle_name" : 'ISELI VALDEZ',
        'sur_name':'URIARTE',
        'dob':'1971-02-26',
        # 'address_line1':'Monterrey Nuevo León 64930.0',
        'addressElement1 (ad1)': '  Culiacán',
        'addressElement2 (sub_district)': ' Sinaloa',
        'addressElement3 (regency)': ' 80000.0',
        'addressElement4 (province)': '',
        "mobile" : '6674196444',
        "email" : ""
    },
              
    # 'indonisia' : {
    #     'db_path': "INDONISIA",
    #     # 'table_name': "INDONISIA_SAMPLE_RECORDS",
    #     'table_name': ['indonisia_1M_records','indonisia_all_records'],
    #     'first_name':'FARHAN',
    #     # "middle_name" : 'GIAN JUWANDA',
    #     "middle_name" : 'GIAN',
    #     'sur_name':'PRATAMA',
    #     'dob':'2003-03-20',
    #     'addressElement1 (ad1)': 'SUSUKAN',
    #     'addressElement2 (sub_district)': 'COMAL',
    #     'addressElement3 (regency)': 'KABUPATEN PEMALANG',
    #     'addressElement4 (province)': 'JAWA TENGAH',
    #     "mobile" : '',
    #     "email" : ""
    # }
    'indonisia' : {
        'db_path': "INDONISIA",
        'id_number': "7402100711490003",
        # 'table_name': "INDONISIA_SAMPLE_RECORDS",
        'table_name': ['indonisia_1M_records','indonisia_all_records'],
        'first_name':'IQBAL',
        # "middle_name" : 'GIAN JUWANDA',
        "middle_name" : 'SEPTIAN',
        'sur_name':'RAHARJA',
        'dob':'1949-11-07',
        'addressElement1 (ad1)': 'ABUKI KAB',
        'addressElement2 (sub_district)': 'KONAWE SULAWESI',
        'addressElement3 (regency)': 'TENGGARA',
        'addressElement4 (province)': '93452',
        "mobile" : '082292231843',
        "email" : ""
    }
}