import pandas as pd
import numpy as np

def is_subset_group(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b
    A group is a subset if ALL its criteria are contained within another group
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False
    
    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]
    
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()
        
        # Handle NaN/empty values
        val_a_empty = pd.isna(group_a[attribute]) or val_a in ['nan', '', 'NaN']
        val_b_empty = pd.isna(group_b[attribute]) or val_b in ['nan', '', 'NaN']
        
        # If both are empty, continue
        if val_a_empty and val_b_empty:
            continue
            
        # If group_b is empty but group_a has values, group_a is NOT a subset
        if val_b_empty and not val_a_empty:
            return False
            
        # If group_a is empty but group_b has values, group_a IS a subset (less restrictive)
        if val_a_empty and not val_b_empty:
            continue
            
        # If both have values, check if group_a values are contained in group_b
        if not val_a_empty and not val_b_empty:
            set_a = set([x.strip() for x in val_a.split(',') if x.strip()])
            set_b = set([x.strip() for x in val_b.split(',') if x.strip()])
            
            if not set_a.issubset(set_b):
                return False
    
    return True

def merge_subset_groups_final(profiles_csv, weights_csv, output_prefix="final"):
    """
    Main function to merge subset groups
    
    Args:
        profiles_csv: Path to profiles CSV file
        weights_csv: Path to weights CSV file
        output_prefix: Prefix for output files
    
    Returns:
        merged_profiles, merged_weights: DataFrames with merged data
    """
    
    print("📂 LOADING DATA...")
    print("="*50)
    
    # Load data
    profiles = pd.read_csv(profiles_csv).set_index('Unnamed: 0')
    weights = pd.read_csv(weights_csv).set_index('Unnamed: 0')
    
    print(f"✅ Data loaded successfully!")
    print(f"   • Profiles shape: {profiles.shape}")
    print(f"   • Weights shape: {weights.shape}")
    
    # Find subset relationships
    print(f"\n🔍 ANALYZING SUBSET RELATIONSHIPS...")
    print("-"*50)
    
    groups = profiles.columns.tolist()
    subset_relationships = []
    
    for group_a in groups:
        for group_b in groups:
            if group_a != group_b:
                if is_subset_group(group_a, group_b, profiles):
                    subset_relationships.append((group_a, group_b))
    
    if not subset_relationships:
        print("❌ No subset relationships found. Returning original data.")
        return profiles.copy(), weights.copy()
    
    print("✅ FOUND SUBSET RELATIONSHIPS:")
    for subset, parent in subset_relationships:
        print(f"   • {subset} ⊆ {parent}")
    
    # Create copies for merging
    merged_profiles = profiles.copy()
    merged_weights = weights.copy()
    groups_to_remove = set()
    
    print(f"\n🔄 MERGING PROCESS:")
    print("-"*50)
    
    # Process each subset relationship
    for subset_group, parent_group in subset_relationships:
        if subset_group in groups_to_remove:
            print(f"⚠️  {subset_group} already processed, skipping...")
            continue
            
        print(f"\n📊 Merging {subset_group} into {parent_group}:")
        
        # 1. Fill empty values in parent profile from subset profile
        filled_count = 0
        for attribute in merged_profiles.index:
            parent_val = merged_profiles.loc[attribute, parent_group]
            subset_val = merged_profiles.loc[attribute, subset_group]
            
            parent_empty = pd.isna(parent_val) or str(parent_val).strip() in ['nan', '', 'NaN']
            subset_empty = pd.isna(subset_val) or str(subset_val).strip() in ['nan', '', 'NaN']
            
            if parent_empty and not subset_empty:
                merged_profiles.loc[attribute, parent_group] = subset_val
                print(f"      • {attribute}: filled '{subset_val}' from {subset_group}")
                filled_count += 1
        
        if filled_count == 0:
            print(f"      • No empty values to fill in {parent_group}")
        
        # 2. Merge weights by averaging
        print(f"   2. Averaging weights...")
        
        subset_weight_col = f"{subset_group}_Relative_Percentage"
        parent_weight_col = f"{parent_group}_Relative_Percentage"
        
        if subset_weight_col in merged_weights.columns and parent_weight_col in merged_weights.columns:
            weight_changes = 0
            for category in merged_weights.index:
                subset_weight = merged_weights.loc[category, subset_weight_col]
                parent_weight = merged_weights.loc[category, parent_weight_col]
                
                # Calculate average
                if pd.isna(subset_weight) and pd.isna(parent_weight):
                    avg_weight = np.nan
                elif pd.isna(subset_weight):
                    avg_weight = parent_weight
                elif pd.isna(parent_weight):
                    avg_weight = subset_weight
                else:
                    avg_weight = (subset_weight + parent_weight) / 2
                    if avg_weight != parent_weight:
                        weight_changes += 1
                
                merged_weights.loc[category, parent_weight_col] = avg_weight
            
            print(f"      • Updated {weight_changes} weight values")
        
        # Mark subset group for removal
        groups_to_remove.add(subset_group)
        print(f"   3. Marked {subset_group} for removal (keeping {parent_group})")
    
    # Remove subset groups
    print(f"\n🗑️  REMOVING SUBSET GROUPS:")
    print("-"*40)
    
    for group_to_remove in groups_to_remove:
        # Remove from profiles
        if group_to_remove in merged_profiles.columns:
            merged_profiles = merged_profiles.drop(columns=[group_to_remove])
            print(f"   ✓ Removed {group_to_remove} from profiles")
        
        # Remove from weights
        weight_col_to_remove = f"{group_to_remove}_Relative_Percentage"
        if weight_col_to_remove in merged_weights.columns:
            merged_weights = merged_weights.drop(columns=[weight_col_to_remove])
            print(f"   ✓ Removed {weight_col_to_remove} from weights")
    
    # Save results
    profiles_output = f"{output_prefix}_profiles.csv"
    weights_output = f"{output_prefix}_weights.csv"
    
    merged_profiles.to_csv(profiles_output)
    merged_weights.to_csv(weights_output)
    
    print(f"\n💾 RESULTS SAVED:")
    print(f"   • {profiles_output}")
    print(f"   • {weights_output}")
    
    print(f"\n🎉 SUMMARY:")
    print("="*50)
    print(f"   • Original groups: {len(profiles.columns)} → Final groups: {len(merged_profiles.columns)}")
    print(f"   • Groups removed: {groups_to_remove}")
    print(f"   • Groups remaining: {list(merged_profiles.columns)}")
    
    return merged_profiles, merged_weights

if __name__ == "__main__":
    # Run the merge for your specific files
    merged_profiles, merged_weights = merge_subset_groups_final(
        profiles_csv="artifacts/2025-05-31T13-27_export (1).csv",
        weights_csv="artifacts/2025-05-31T13-27_export.csv",
        output_prefix="final_merged"
    )
