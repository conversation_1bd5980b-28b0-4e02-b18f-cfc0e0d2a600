# Import python packages
import streamlit as st
from snowflake.snowpark import Session # type: ignore
import streamlit as st
import datetime

from utils import *
import time

st.set_page_config(
    page_title="IDV",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="collapsed")

selection  = st.radio('Source Country Selection', ['Indonesia','Mexico','Australia'],horizontal=True)

if selection == 'Indonesia':
    country(country_prefix='indonisia')

elif selection == 'Mexico':
    country(country_prefix='mx')

    # # Get the current credentials
    # session = Session.builder.getOrCreate()

    # #  Create an example data frame
    # data_frame = session.sql("SELECT * FROM shared_data.INDONISIA_TEST limit 10")

    # # Execute the query and convert it into a Pandas data frame
    # queried_data = data_frame.to_pandas()

    # # Display the Pandas data frame as a Streamlit data frame.
    # st.dataframe(queried_data, use_container_width=True)
    # st.write("Data loaded successfully")
    # st.write(multiply(4,6))

