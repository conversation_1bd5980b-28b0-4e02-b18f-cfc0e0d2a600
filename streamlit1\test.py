       if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('Phone2_Mobile', '')) and not pd.isna(row.get('EmailAddress', '')):
        # st.write("row.get('DPID', '')", row.get('DPID', ''))
        # if not pd.isna(row.get('DPID', '')) and pd.isna(row.get('enriched', '')):
        # if  not pd.isna(row.get('Phone2_Mobile', '')):
        #     st.write("processing step 2.1")
            # matches = bsm_df[
            #         # (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
            #         (bsm_df["DPID_MERGED"] == row.get('DPID', '')) &
            #         (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
            #         # (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
            #         (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
            #     ]
            # Find the correct column names in BSM data
            dpid_col = None
            phone_col = None
            email_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
                    phone_col = col
                elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
                    email_col = col

        #     # Only proceed if we found all required columns
            if dpid_col and phone_col and email_col:
                # st.write("processing step 2.22")
                # Ensure consistent formatting for comparison
                # dpid_value = "77812002.0".strip()
                # bsm_df[dpid_col] = bsm_df[dpid_col].astype(str).str.strip()
                # st.write("bsm", bsm_df[bsm_df[dpid_col] == dpid_value])
                # st.write("row['DPID']", str(row.get('DPID', '')))
                # st.write("bsm_df[dpid_col]", str(bsm_df[dpid_col].iloc[0]))
                # st.write("row['DPID'] type", type(row.get('DPID', '')))
                # st.write("bsm_df[dpid_col] type", bsm_df[dpid_col].iloc[0].dtype)
        #         st.write("bsm_df[dpid_col", bsm_df[dpid_col].head())
        #         st.write("row['DPID']", row.get('DPID', ''))
        #         st.write("bsm_df[phone_col]", bsm_df[phone_col].head())
        #         st.write("row['Phone2_Mobile']", row['Phone2_Mobile'])
        #         st.write("bsm_df[email_col]", bsm_df[email_col].head())
        #         st.write("row['EmailAddress']", row['EmailAddress'])
        #         st.write("dpid match", bsm_df[dpid_col].astype(str) == str(row.get('DPID', '')))
        #         st.write("phone match", bsm_df[phone_col].astype(str) == str(row['Phone2_Mobile']))
        #         st.write("email match", bsm_df[email_col].astype(str) == str(row['EmailAddress']))
                matches = bsm_df[
                    # (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
                    (bsm_df[dpid_col].astype(str) == str(row.get('DPID', ''))) &
                    (bsm_df[phone_col].astype(str) == str(row.get('Phone2_Mobile',''))) &
                    # (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
                    (bsm_df[email_col].astype(str) == str(row.get('EmailAddress')))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found
        #     st.write("processing step 2.3")
        #     st.write(f"Matches found: {matches.head()}")
            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+Mobile+EmailAddress'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Mobile+EmailAddress'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+Mobile+EmailAddress'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Mobile+EmailAddress'

                continue  # Move to next row if match found
        # Step 1: Match by Ad1, Suburb, State, Postcode, Phone2_Mobile, EmailAddress
        if not pd.isna(row.get('Ad1', '')) and not pd.isna(row.get('Suburb', '')) and not pd.isna(row.get('State', '')) and not pd.isna(row.get('Postcode', '')):
            # Find the correct column names in BSM data (handle case variations)
            ad1_col = None
            suburb_col = None
            state_col = None
            postcode_col = None

            for col in bsm_df.columns:
                if col.upper() in ['AD1', 'ADDRESS1']:
                    ad1_col = col
                elif col.upper() in ['SUBURB']:
                    suburb_col = col
                elif col.upper() in ['STATE']:
                    state_col = col
                elif col.upper() in ['POSTCODE']:
                    postcode_col = col

            # Only proceed if we found all required columns
            if ad1_col and suburb_col and state_col and postcode_col:
                matches = bsm_df[
                    (bsm_df[ad1_col].astype(str).str.lower() == str(row['Ad1']).lower()) &
                    (bsm_df[suburb_col].astype(str).str.lower() == str(row['Suburb']).lower()) &
                    (bsm_df[state_col].astype(str).str.lower() == str(row['State']).lower()) &
                    (bsm_df[postcode_col].astype(str) == str(row['Postcode']))
                ]
            
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            # st.write(f"Matches found for Ad1, Suburb, State, Postcode: {matches.head()}")
            # st.write("phone null", pd.isna(row.get('Phone2_Mobile', '')))
            # st.write("email null", pd.isna(row.get('EmailAddress', '')))
            # matches['Phone2_Mobile'.upper()] = matches['Phone2_Mobile'.upper()].fillna(np.nan)
            # matches['EmailAddress'.upper()] = matches['EmailAddress'.upper()].fillna(np.nan)
            # st.write("matces", matches)
            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            # st.write("matches", matches["PHONE2_MOBILE"].unique())
            # st.write("matches email", matches["EMAILADDRESS"].unique())
            # st.write("matching mobile null", (matches['PHONE2_MOBILE'].isna()).sum())
            # st.write("matching email null", (matches['EMAILADDRESS'].isna()).sum())

            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'Ad1+Suburb+State+Postcode'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'Ad1+Suburb+State+Postcode'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'Ad1+Suburb+State+Postcode'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'Ad1+Suburb+State+Postcode'

                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'Ad1+Suburb+State+Postcode'
                #     enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                #     enriched_df.at[idx, 'Mobile_Verified'] = 'Ad1+Suburb+State+Postcode'
                
                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'Ad1+Suburb+State+Postcode'
                continue  # Move to next row if match found

        # Step 2: Match by DPID, Phone2_Mobile, EmailAddress
        if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('Phone2_Mobile', '')) and not pd.isna(row.get('EmailAddress', '')):
        # st.write("row.get('DPID', '')", row.get('DPID', ''))
        # if not pd.isna(row.get('DPID', '')) and pd.isna(row.get('enriched', '')):
        # if  not pd.isna(row.get('Phone2_Mobile', '')):
        #     st.write("processing step 2.1")
            # matches = bsm_df[
            #         # (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
            #         (bsm_df["DPID_MERGED"] == row.get('DPID', '')) &
            #         (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
            #         # (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
            #         (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
            #     ]
            # Find the correct column names in BSM data
            dpid_col = None
            phone_col = None
            email_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
                    phone_col = col
                elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
                    email_col = col

        #     # Only proceed if we found all required columns
            if dpid_col and phone_col and email_col:
                # st.write("processing step 2.22")
                # Ensure consistent formatting for comparison
                # dpid_value = "77812002.0".strip()
                # bsm_df[dpid_col] = bsm_df[dpid_col].astype(str).str.strip()
                # st.write("bsm", bsm_df[bsm_df[dpid_col] == dpid_value])
                # st.write("row['DPID']", str(row.get('DPID', '')))
                # st.write("bsm_df[dpid_col]", str(bsm_df[dpid_col].iloc[0]))
                # st.write("row['DPID'] type", type(row.get('DPID', '')))
                # st.write("bsm_df[dpid_col] type", bsm_df[dpid_col].iloc[0].dtype)
        #         st.write("bsm_df[dpid_col", bsm_df[dpid_col].head())
        #         st.write("row['DPID']", row.get('DPID', ''))
        #         st.write("bsm_df[phone_col]", bsm_df[phone_col].head())
        #         st.write("row['Phone2_Mobile']", row['Phone2_Mobile'])
        #         st.write("bsm_df[email_col]", bsm_df[email_col].head())
        #         st.write("row['EmailAddress']", row['EmailAddress'])
        #         st.write("dpid match", bsm_df[dpid_col].astype(str) == str(row.get('DPID', '')))
        #         st.write("phone match", bsm_df[phone_col].astype(str) == str(row['Phone2_Mobile']))
        #         st.write("email match", bsm_df[email_col].astype(str) == str(row['EmailAddress']))
                matches = bsm_df[
                    # (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
                    (bsm_df[dpid_col].astype(str) == str(row.get('DPID', ''))) 
                    (bsm_df[phone_col].astype(str) == str(row['Phone2_Mobile']))&
                    # (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
                    (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found
        #     st.write("processing step 2.3")
        #     st.write(f"Matches found: {matches.head()}")
            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+Mobile+EmailAddress'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Mobile+EmailAddress'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+Mobile+EmailAddress'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Mobile+EmailAddress'

                continue  # Move to next row if match found

        # # Step 3: Match by DPID, Phone2_Mobile
        if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('Phone2_Mobile', '')):
            # Find the correct column names in BSM data
            dpid_col = None
            phone_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
                    phone_col = col

            # Only proceed if we found all required columns
            if dpid_col and phone_col:
                matches = bsm_df[
                    (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
                    (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower())
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+Mobile'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Mobile'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+Mobile'

                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'DPID+Mobile'
                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Phone2_Mobile'

                continue  # Move to next row if match found

        # Step 4: Match by DPID, EmailAddress
        if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('EmailAddress', '')):
            # Find the correct column names in BSM data
            dpid_col = None
            email_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
                    email_col = col

            # Only proceed if we found all required columns
            if dpid_col and email_col:
                matches = bsm_df[
                    (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
                    (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+EmailAddress'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+EmailAddress'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+EmailAddress'

                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'DPID+EmailAddress'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+EmailAddress'

                continue

        if not (pd.isna(row.get('First_Name', ''))) and not(pd.isna(row.get('Surname', ''))) and  not pd.isna(row.get('EmailAddress', '')):
           # Find the correct column names in BSM data
            First_Name_col = "GN_1_1"
            Surname_col = "SN_1_1"
            email_col = "EMAILADDRESS"

            if First_Name_col and Surname_col and email_col:
                matches = bsm_df[
                    (bsm_df[First_Name_col].astype(str).str.lower() == str(row['First_Name']).lower()) &
                    (bsm_df[Surname_col].astype(str) == str(row['Surname'])) &
                    (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            if len(matches) > 0:
                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'FullName+EmailAddress'

            continue  # Move to next row if match found


        if not (pd.isna(row.get('First_Name', ''))) and not(pd.isna(row.get('Surname', ''))) and  not pd.isna(row.get('Phone2_Mobile', '')):
           # Find the correct column names in BSM data
            First_Name_col = "GN_1_1"
            Surname_col = "SN_1_1"
            mobile_col = "PHONE2_MOBILE"

            if First_Name_col and Surname_col and email_col:
                matches = bsm_df[
                    (bsm_df[First_Name_col].astype(str).str.lower() == str(row['First_Name']).lower()) &
                    (bsm_df[Surname_col].astype(str) == str(row['Surname'])) &
                    (bsm_df[mobile_col].astype(str) == str(row['Phone2_Mobile']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            if len(matches) > 0:
                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'FullName+Mobile'

            continue  # Move to next row if match found



    col1, col2, col3, col4 = st.columns(4)

    with col1:
        age_filled = (enriched_data['Age_Verified'] != '').sum()
        age_missing = (enriched_data['Age'] == '').sum() + enriched_data['Age'].isna().sum()
        st.metric("Age Values Filled", age_filled)
        # st.write(f"Missing Age values: {age_missing}")

        # Show verification methods
        age_methods = enriched_data['Age_Verified'].value_counts().reset_index()
        if not age_methods.empty and age_methods.iloc[0, 0] != '':
            st.write("Verification Methods:")
            for idx, row in age_methods.iterrows():
                if row['index'] != '':
                    st.write(f"- {row['index']}: {row['Age_Verified']} records")

    with col2:
        gender_filled = (enriched_data['Gender_Verified'] != '').sum()
        gender_missing = (enriched_data['Gender'] == '').sum() + enriched_data['Gender'].isna().sum()
        st.metric("Gender Values Filled", gender_filled)
        # st.write(f"Missing Gender values: {gender_missing}")

        # Show verification methods
        gender_methods = enriched_data['Gender_Verified'].value_counts().reset_index()
        if not gender_methods.empty and gender_methods.iloc[0, 0] != '':
            st.write("Verification Methods:")
            for idx, row in gender_methods.iterrows():
                if row['index'] != '':
                    st.write(f"- {row['index']}: {row['Gender_Verified']} records")

    with col3:
        income_filled = (enriched_data['Income_Verified'] != '').sum()
        income_missing = (enriched_data['Income'] == '').sum() + enriched_data['Income'].isna().sum()
        st.metric("Income Values Filled", income_filled)
        # st.write(f"Missing Income values: {income_missing}")

        # Show verification methods
        income_methods = enriched_data['Income_Verified'].value_counts().reset_index()
        if not income_methods.empty and income_methods.iloc[0, 0] != '':
            st.write("Verification Methods:")
            for idx, row in income_methods.iterrows():
                if row['index'] != '':
                    st.write(f"- {row['index']}: {row['Income_Verified']} records")

    with col4:
        homeowner_filled = (enriched_data['HomeOwner_Verified'] != '').sum()
        homeowner_missing = (enriched_data.get('HomeOwner', pd.Series(dtype='object')) == '').sum() + enriched_data.get('HomeOwner', pd.Series(dtype='object')).isna().sum()
        st.metric("HomeOwner Values Filled", homeowner_filled)
        # st.write(f"Missing HomeOwner values: {homeowner_missing}")

        # # Show verification methods
        # homeowner_methods = enriched_data['HomeOwner_Verified'].value_counts().reset_index()
        # if not homeowner_methods.empty and homeowner_methods.iloc[0, 0] != '':
        #     st.write("Verification Methods:")
        #     for idx, row in homeowner_methods.iterrows():
        #         if row['index'] != '':
        #             st.write(f"- {row['index']}: {row['HomeOwner_Verified']} records")

    with col1:
        mobile_filled = (enriched_data['Mobile_Verified'] != '').sum()
        mobile_missing = (enriched_data['Phone2_Mobile'] == '').sum() + enriched_data['Phone2_Mobile'].isna().sum()
        st.metric("Mobile Values Filled", mobile_filled)
        # st.write(f"Missing Mobile values: {mobile_missing}")

    with col2:
        email_filled = (enriched_data['EmailAddress_Verified'] != '').sum()
        email_missing = (enriched_data['EmailAddress'] == '').sum() + enriched_data['EmailAddress'].isna().sum()
        st.metric("Email Values Filled", email_filled)


    st.markdown("""
    This application enriches your customer data by appending additional columns useful for profiling, such as Age, Gender, Income, and HomeOwner.
    The enrichment process uses a reference dataset and applies multiple levels of matching criteria to fill in missing values.
    If any of these columns are missing from your data, they will be automatically added and populated where possible.
    1. **Level 1**: FullName + DPID
    2. **Level 2**: DPID + Mobile
    3. **Level 3**: DPID + Email
    4. **Level 4**: Mobile
    5. **Level 5**: Email
    6. **Level 6**: DPID
    7. **Level 7**: Address + Postcode
    """)


import streamlit as st
import pandas as pd
import numpy as np
import datetime
import ast
# from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import LabelEncoder, StandardScaler
from utils import *
from profile_extraction import *
from snowflake.snowpark import Session
from data_enrich import enrich_data


# # # Set page configuration
# st.set_page_config(
#     page_title="SimilarSeek",
#     page_icon="📊",
#     layout="wide"
# )
# App title and description
st.title("SimilarSeek")
st.markdown("""
Lookalike modeling uses your customer data to find new customers similar to your best ones.\n

""")

st.markdown("Industry selected – :green[Charity]")
data = st.file_uploader("Upload your customer data file", type=["csv", "xlsx"])
if data is not None:
    if data.name.endswith('.csv'):
        data = pd.read_csv(data,encoding='latin1', nrows=500).fillna('')
    elif data.name.endswith('.xlsx'):
        # If the file is an Excel file, read the first sheet
        data = pd.read_excel(data, sheet_name=0, encoding='latin1').fillna('')
    else:
        st.error("Unsupported file format. Please upload a CSV or Excel file.")
        st.stop()
    with st.expander("Data Preview"):
        st.dataframe(data.head(3),use_container_width=True)

    session = Session.builder.getOrCreate()
    bsm_concat = session.sql("select * from code_schema.bsm_view_all;").to_pandas()

    st.markdown("""
    This application demonstrates how to enrich customer data with missing Age, Gender, Income, and HomeOwner values
    by matching records with a reference dataset using multiple levels of matching criteria.

    1. **Level 1**: FullName + DPID
    2. **Level 2**: DPID + Mobile
    3. **Level 3**: DPID + Email
    4. **Level 4**: Mobile
    5. **Level 5**: Email
    6. **Level 6**: DPID
    7. **Level 7**: Address + Postcode
    """)

    with st.spinner("Enriching data..."):
        # Enrich the data
        enriched_data = enrich_data(data, bsm_concat)

    # # Display enriched data
    # st.header("Enriched Data")
    # st.dataframe(enriched_data)

    # Display statistics
    st.header("Enrichment Statistics")

    # Calculate statistics for each field
    stats = []
    total_records = len(enriched_data)

    def filled_count(col, verified_col=None):
        filled = (enriched_data[col] != '').sum() + enriched_data[col].notna().sum() - (enriched_data[col] == '').sum()
        if verified_col and verified_col in enriched_data.columns:
            filled_verified = (enriched_data[verified_col] != '').sum()
            return filled, filled_verified
        return filled, None

    fields = [
        ("Age", "Age_Verified"),
        ("Gender", "Gender_Verified"),
        ("Income", "Income_Verified"),
        ("HomeOwner", "HomeOwner_Verified"),
        ("Phone2_Mobile", "Mobile_Verified"),
        ("EmailAddress", "EmailAddress_Verified"),
    ]

    for col, verified_col in fields:
        base_filled = (enriched_data[col] != '').sum()
        enriched_filled = (enriched_data[verified_col] != '').sum() if col in data.columns else 0
        percent = (enriched_filled / total_records * 100) if total_records else 0
        # increment = enriched_filled - base_filled
        # increment_percent = ((increment / base_filled) * 100) if base_filled else 0
        stats.append({
            "Field": col.replace("Phone2_Mobile", "Mobile").replace("EmailAddress", "Email"),
            "Total Records": total_records,
            "Filled Before": base_filled if col in data.columns else 0,
            "Filled After": (enriched_data[verified_col] != '').sum(),
            # "Enrichment (%)": f"{percent:.1f}%"
            "Enrichment (%)": f"{(enriched_data[verified_col] != '').sum() / total_records * 100:.1f}%"
        })

    stats_df = pd.DataFrame(stats)
    st.dataframe(stats_df.set_index("Field"), use_container_width=True)
        # st.write(f"Missing Email values: {email_missing}")
    # Download option for enriched data
    # csv = enriched_data.to_csv(index=False)
    # st.download_button(
    #     label="Download Enriched Data as CSV",
    #     data=csv,
    #     file_name="enriched_data.csv",
    #     mime="text/csv",
    # )
    # st.markdown("---enrichment completed---")
    # st.dataframe(enriched_data, use_container_width=True)
    population_filter_demog, weights_for_profiles,profiles = lookalike_profile_extraction(enriched_data)
  
    # profiles = profiles.replace({}, np.nan)
    # profiles = profiles.replace('',np.nan)

    use_index = [col for col in ['Income', 'HomeOwner', 'Gender', 'Age'] if col in profiles.index]
    
    population_filter_demog = population_filter_demog.loc[use_index]
    profiles = profiles.loc[use_index]
    st.write("Sample Profiles:")
    st.dataframe( population_filter_demog, use_container_width=True)
    # st.write("weights_for_profiles", weights_for_profiles)
    # st.write("profiles", profiles)

    row_unique_values_dict = {
        index: list(set(row.dropna().values)) for index, row in profiles.iterrows()
    }
    row_unique_values_dict = {
        key: [item.strip() for sublist in value for item in sublist.split(', ')] 
        for key, value in row_unique_values_dict.items()
    }
    row_unique_values_dict = {
        key: list(set(value)) for key, value in row_unique_values_dict.items()
    }
    row_unique_values_dict = {
        key: [item for item in value if item.strip()] for key, value in row_unique_values_dict.items()
    }
    population_filter_demog = {key: value for key, value in row_unique_values_dict.items() if key in ['Age', 'Gender', 'HomeOwner', 'Income', 'Group_percentage']}
    population = bsm_concat[
    (bsm_concat['Age1'.upper()].isin(population_filter_demog['Age'])) &
    (bsm_concat['Gender1'.upper()].isin(population_filter_demog['Gender'])) &
    (bsm_concat['HomeOwner1'.upper()].isin(population_filter_demog['HomeOwner'])) &
    (bsm_concat['Income1'.upper()].isin(population_filter_demog['Income']))
    ]
    # st.write("Population size:", population.shape[0])
    # st.write("Population preview:")
    # st.dataframe(population.head(3), use_container_width=True)


    #-------------------------
    result = {
    col: weights_for_profiles[col].to_dict()
    for col in weights_for_profiles.drop(columns=["Category"]).columns
    }
    result = {
        key.replace('_Relative_Percentage', ''): value
        for key, value in result.items()
    }
    # st.write("Weights for profiles:", result)


    for group in result.keys():  # Iterate over Group_1, Group_2, etc.
        population[group] = population.apply(
            lambda row: sum(
                result[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
            ),
            axis=1
        )

    
    group_columns = [col for col in population.columns if col.startswith('Group_')]
    population['max_value'] = population[group_columns].max(axis=1)
    population['selected_group'] = population[group_columns].idxmax(axis=1)


    from sklearn.preprocessing import MinMaxScaler

    # Initialize the MinMaxScaler
    scaler = MinMaxScaler()

    # Select the columns to normalize
    columns_to_normalize = [
        col for col in weights_for_profiles.columns if 'Relative_Percentage' in col
    ]

    # Group by 'Category' and apply MinMaxScaler to each group
    weights_for_profiles[[f'normalized_{col}' for col in columns_to_normalize]] = (
        weights_for_profiles[columns_to_normalize]
        .div(weights_for_profiles[columns_to_normalize].sum(axis=1), axis=0)
    )

    result1 = {
    col: weights_for_profiles[col].to_dict()
    for col in weights_for_profiles.drop(columns=["Category"]).columns
    }
    result1 = {
        key.replace('_Relative_Percentage', ''): value
        for key, value in result1.items()
    }
    result1 = {
        key.replace('normalized_', ''): value
        for key, value in result1.items()
    }

    for group in result1.keys():  # Iterate over Group_1, Group_2, etc.
        population[group] = population.apply(
            lambda row: sum(
                result1[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
            ),
            axis=1
        )

    
    population['max_value'] = population[group_columns].max(axis=1)
    population['selected_group'] = population[group_columns].idxmax(axis=1)

    #----------
    population['GIFT_CASUSE_PREFENCE_Flag'] = population['GIFT_CASUSE_PREFENCE'].apply(lambda x: 1 if x != '' else 0)
    bsm_segments_list = [
        'Segment_8DD_Festivals',
        'Segment_Facebook_Music',
        'Segment_Facebook_Music_Fantastic',
        'Travel_Domestic',
        'Travel_International',
        'Segment_8DD_Travel_Domestic_Air',
        'Segment_8DD_Travel_Domestic_Rail',
        'Segment_8DD_football_spectator',
        'Segment_8DD_tennis_spectator',
        'Segment_8DD_rugby_spectator',
        'Segment_8DD_Debit_Card',
        'Segment_8DD_Invest_Crypto',
        'Segment_8DD_Invest_Shares',
        'charity_donor_online',
        'charity_donor_facetoface'
    ]
    bsm_segments_list = [segment.upper() for segment in bsm_segments_list]

    # df2['BSM_segment_Flag'] = df2[bsm_segments_list].apply(lambda row: row.eq('Y').any(), axis=1).astype(int)
    population['BSM_segments_Flag'] = population[bsm_segments_list].apply(lambda row: row.eq('Y').sum(), axis=1)

    bsm_donor_involved = ['Charity_6Months',
    'Charity_12Months',
    'Charity_24Months',
    'Charity_24Months_More']
    bsm_donor_involved = [segment.upper() for segment in bsm_donor_involved]

    population['BSM_doner_involved_Flag'] = population[bsm_donor_involved].apply(lambda row: row.eq(1).sum(), axis=1)

    population['BSM_total_score'] = population['BSM_segments_Flag']*0.1 + population['BSM_doner_involved_Flag']*0.3 + population['max_value']*0.6

    # st.write("Population with BSM segments flag:")
    # st.dataframe(population.head(3), use_container_width=True)

#-------------------------
    digi_merge = session.sql("select * from code_schema.digi_merge_view;").to_pandas()
    population['POSTCODE'] = population['POSTCODE'].astype(str)
    # digi_merge['postcode'] = digi_merge['postcode'].astype(str)
    digi_merge['POSTCODE'] = digi_merge['POSTCODE'].astype(str).str.replace('.0', '', regex=False)
    digi_merge['address_key'] = digi_merge[['AD1', 'SUBURB', 'STATE', 'POSTCODE']].astype(str).agg('|'.join, axis=1)
    digi_merge_sorted = digi_merge.sort_values(
        by=['address_key', 'full_score'.upper()],
        ascending=[True, False]
    )
    digi_merge_sorted = digi_merge_sorted.drop_duplicates(subset='address_key', keep='first')
    merged_df = population.merge(
        digi_merge_sorted[['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper(), 'full_score'.upper()]],
        left_on=['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper()],
        right_on=['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper()],
        how='left',
        indicator=True
    )
    merged_df['full_score'.upper()] = merged_df['full_score'.upper()].fillna(merged_df['BSM_total_score'])
    merged_df['Final_score'.upper()] = merged_df['BSM_total_score'] + merged_df['full_score'.upper()]
    merged_df_sorted = merged_df.sort_values('Final_score'.upper(), ascending=False)
    # st.write("Merged DataFrame with final scores:")
    # st.dataframe(merged_df_sorted.head(3), use_container_width=True)


#-------------------------
    st.markdown(f"Total records:- :green[{len(merged_df_sorted)}]")
    no_of_records =st.number_input("No of records to filter", min_value=1, max_value=len(merged_df_sorted), value=10000, step=100, help="Number of records to filter based on the selected percentage")
    st.text("Higher scores reflect stronger alignment with your current customer profile, while lower scores indicate weaker similarity")
    # top_25_percent = merged_df_sorted.iloc[:int(len(merged_df_sorted) * top_percentage/100)]
    
    
    top_25_percent = merged_df_sorted.iloc[:no_of_records]
    top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].astype(str).str.replace('.0', '', regex=False)
    top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

    top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].astype(str).str.replace('.0', '', regex=False)
    top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

    top_25_percent['EmailAddress'.upper()] = top_25_percent['EmailAddress'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if isinstance(x, str) and len(x) > 0 else x)

    # Filter rows where DSAVerified is 'Yes' and sort by Final_score in descending order
    filtered_sorted_df = top_25_percent.sort_values(
        by=['DSAVerified'.upper(), 'Final_score'.upper()], 
        ascending=[False, False],
        key=lambda col: col.fillna('') if col.name == 'DSAVerified'.upper() else col
    )


    # Write the filtered and sorted DataFrame to a CSV file
    # filtered_sorted_df.to_csv('filtered_sorted_output.csv', index=False)

    st.dataframe(filtered_sorted_df[['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper(), 'Phone1_Landline'.upper(), 'Phone2_Mobile'.upper(),
            'EmailAddress'.upper(), 'selected_group', 'DSAVerified'.upper(), 'Final_score'.upper()]].head(10).reset_index(drop=True),use_container_width=True)