import pandas as pd
import numpy as np

def merge_subset_groups_simple(profiles_csv, weights_csv, output_prefix="merged"):
    """
    Simple function to merge subset groups
    
    Args:
        profiles_csv: Path to profiles CSV file
        weights_csv: Path to weights CSV file  
        output_prefix: Prefix for output files
    """
    
    # Load data
    print("📂 Loading data...")
    profiles = pd.read_csv(profiles_csv).set_index('Unnamed: 0')
    weights = pd.read_csv(weights_csv).set_index('Unnamed: 0')
    
    print(f"   • Profiles: {profiles.shape}")
    print(f"   • Weights: {weights.shape}")
    
    # Find subset relationships (simplified logic)
    groups = profiles.columns.tolist()
    subset_relationships = []
    
    print(f"\n🔍 Checking subset relationships...")
    
    for group_a in groups:
        for group_b in groups:
            if group_a != group_b:
                is_subset = True
                
                for attribute in profiles.index:
                    val_a = str(profiles.loc[attribute, group_a]).strip()
                    val_b = str(profiles.loc[attribute, group_b]).strip()
                    
                    # Skip empty values
                    if val_a in ['nan', '', 'NaN'] or val_b in ['nan', '', 'NaN']:
                        continue
                    
                    # Check if group_a values are contained in group_b
                    set_a = set([x.strip() for x in val_a.split(',') if x.strip()])
                    set_b = set([x.strip() for x in val_b.split(',') if x.strip()])
                    
                    if not set_a.issubset(set_b):
                        is_subset = False
                        break
                
                if is_subset:
                    subset_relationships.append((group_a, group_b))
    
    if not subset_relationships:
        print("❌ No subset relationships found!")
        return profiles, weights
    
    print("✅ Found subset relationships:")
    for subset, parent in subset_relationships:
        print(f"   • {subset} ⊆ {parent}")
    
    # Merge process
    merged_profiles = profiles.copy()
    merged_weights = weights.copy()
    groups_to_remove = set()
    
    print(f"\n🔄 Merging process:")
    
    for subset_group, parent_group in subset_relationships:
        if subset_group in groups_to_remove:
            continue
            
        print(f"\n   Merging {subset_group} → {parent_group}")
        
        # 1. Fill empty values in parent profile
        for attribute in merged_profiles.index:
            parent_val = str(merged_profiles.loc[attribute, parent_group]).strip()
            subset_val = str(merged_profiles.loc[attribute, subset_group]).strip()
            
            if parent_val in ['nan', '', 'NaN'] and subset_val not in ['nan', '', 'NaN']:
                merged_profiles.loc[attribute, parent_group] = subset_val
                print(f"      • Filled {attribute}: {subset_val}")
        
        # 2. Average weights
        subset_col = f"{subset_group}_Relative_Percentage"
        parent_col = f"{parent_group}_Relative_Percentage"
        
        if subset_col in merged_weights.columns and parent_col in merged_weights.columns:
            for category in merged_weights.index:
                subset_weight = merged_weights.loc[category, subset_col]
                parent_weight = merged_weights.loc[category, parent_col]
                
                # Calculate average
                if pd.isna(subset_weight) and pd.isna(parent_weight):
                    avg_weight = np.nan
                elif pd.isna(subset_weight):
                    avg_weight = parent_weight
                elif pd.isna(parent_weight):
                    avg_weight = subset_weight
                else:
                    avg_weight = (subset_weight + parent_weight) / 2
                
                merged_weights.loc[category, parent_col] = avg_weight
        
        groups_to_remove.add(subset_group)
    
    # 3. Remove subset groups
    print(f"\n🗑️  Removing groups: {groups_to_remove}")
    
    for group in groups_to_remove:
        if group in merged_profiles.columns:
            merged_profiles = merged_profiles.drop(columns=[group])
        
        weight_col = f"{group}_Relative_Percentage"
        if weight_col in merged_weights.columns:
            merged_weights = merged_weights.drop(columns=[weight_col])
    
    # Save results
    profiles_output = f"{output_prefix}_profiles.csv"
    weights_output = f"{output_prefix}_weights.csv"
    
    merged_profiles.to_csv(profiles_output)
    merged_weights.to_csv(weights_output)
    
    print(f"\n💾 Results saved:")
    print(f"   • {profiles_output}")
    print(f"   • {weights_output}")
    
    print(f"\n📊 Summary:")
    print(f"   • Original groups: {len(profiles.columns)} → Final groups: {len(merged_profiles.columns)}")
    print(f"   • Removed: {groups_to_remove}")
    print(f"   • Remaining: {list(merged_profiles.columns)}")
    
    return merged_profiles, merged_weights

if __name__ == "__main__":
    # Run the merge
    merged_profiles, merged_weights = merge_subset_groups_simple(
        profiles_csv="artifacts/2025-05-31T13-27_export (1).csv",
        weights_csv="artifacts/2025-05-31T13-27_export.csv",
        output_prefix="final_merged"
    )
    
    print(f"\n🎉 Merge complete!")
    print("\nFinal Profiles:")
    print(merged_profiles)
    print("\nFinal Weights (first 10 rows):")
    print(merged_weights.head(10))
