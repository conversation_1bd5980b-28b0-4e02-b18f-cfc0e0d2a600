#!/usr/bin/env python3

import pandas as pd
import sys
import os

# Test the HomeOwner enrichment functionality
def homeowner_enrichment(sample_data, bsm_data):
    print("Testing HomeOwner enrichment functionality...")
    
    # Create sample customer data WITHOUT HomeOwner column
    # sample_data = pd.DataFrame({
    #     'FirstName': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    #     'Surname': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    #     'Income': ['$104,000 - $155,999', '', '$65,000 - $77,999', '', '$78,000 - $103,999'],
    #     'Gender': ['male', 'female', '', 'female', ''],
    #     'Age': ['40-44', '', '50-54', '', '35-39'],
    #     'Ad1': ['123 Main St', '456 Oak Ave', '789 Pine Rd', '101 Elm St', '202 Maple Dr'],
    #     'Suburb': ['SYDNEY', 'MELBOURNE', 'BRISBANE', 'PERTH', 'ADELAIDE'],
    #     'State': ['NSW', 'VIC', 'QLD', 'WA', 'SA'],
    #     'Postcode': ['2000', '3000', '4000', '6000', '5000'],
    #     'DPID': ['12345678', '23456789', '34567890', '45678901', '56789012'],
    #     'Phone2_Mobile': ['0412345678', '0423456789', '', '0445678901', ''],
    #     'EmailAddress': ['<EMAIL>', '', '<EMAIL>', '', '<EMAIL>']
    # })
    
    # # Create sample BSM data WITH HomeOwner1 column
    # bsm_data = pd.DataFrame({
    #     'Age1': ['30-34', '40-44', '50-54', '25-29', '60-64'],
    #     'Gender1': ['male', 'female', 'male', 'female', 'male'],
    #     'Income1': ['$104,000 - $155,999', '$156,000+', '$65,000 - $77,999', '$41,600 - $64,999', '$78,000 - $103,999'],
    #     'HomeOwner1': ['HomeOwner', 'renters', 'HomeOwner', 'renters', 'HomeOwner'],
    #     'AD1': ['123 Main St', '456 Oak Ave', '789 Pine Rd', '101 Elm St', '202 Maple Dr'],
    #     'SUBURB': ['SYDNEY', 'MELBOURNE', 'BRISBANE', 'PERTH', 'ADELAIDE'],
    #     'STATE': ['NSW', 'VIC', 'QLD', 'WA', 'SA'],
    #     'POSTCODE': ['2000', '3000', '4000', '6000', '5000'],
    #     'DPID': ['12345678', '23456789', '34567890', '45678901', '56789012'],
    #     'PHONE2_MOBILE': ['0412345678', '0423456789', '0434567890', '0445678901', '0456789012'],
    #     'EMAILADDRESS': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    # })
    
    print("Sample data columns:", list(sample_data.columns))
    print("BSM data columns:", list(bsm_data.columns))
    print("HomeOwner column in sample data:", 'HomeOwner' in sample_data.columns)
    print("HomeOwner1 column in BSM data:", 'HomeOwner1' in bsm_data.columns)
    
    # Import the enrichment function
    from data_enrich import enrich_data
    
    # Test enrichment
    print("\nRunning enrichment...")
    enriched_data = enrich_data(sample_data, bsm_data)
    
    print("Enriched data columns:", list(enriched_data.columns))
    print("HomeOwner column added:", 'HomeOwner' in enriched_data.columns)
    
    # Check results
    print("\nHomeOwner enrichment results:")
    homeowner_filled = (enriched_data['HomeOwner_Verified'] != '').sum()
    print(f"HomeOwner values filled: {homeowner_filled}")
    
    if homeowner_filled > 0:
        print("\nVerification levels:")
        print(enriched_data['HomeOwner_Verified'].value_counts())
        
        print("\nSample enriched records:")
        filled_records = enriched_data[enriched_data['HomeOwner_Verified'] != '']
        if len(filled_records) > 0:
            print(filled_records[['FirstName', 'Surname', 'HomeOwner', 'HomeOwner_Verified']].head())
    
    print("\nAll enriched data:")
    print(enriched_data[['FirstName', 'Surname', 'HomeOwner', 'HomeOwner_Verified']])
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    homeowner_enrichment()
