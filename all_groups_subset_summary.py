import pandas as pd

def is_subset_group_quiet(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b (without detailed output)
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False
    
    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]
    
    # Check each attribute/index
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()
        
        # If group_b has "ALL", it contains everything, so group_a is subset
        if val_b == "ALL":
            continue
            
        # If group_a has "ALL" but group_b doesn't, then group_a is NOT a subset
        if val_a == "ALL" and val_b != "ALL":
            return False
            
        # If both have specific values, check if group_a values are contained in group_b
        if val_a != "ALL" and val_b != "ALL":
            # Split by comma and clean whitespace
            set_a = set([x.strip() for x in val_a.split(',')])
            set_b = set([x.strip() for x in val_b.split(',')])
            
            if not set_a.issubset(set_b):
                return False
    
    return True

def analyze_all_subset_relationships(profiles_df):
    """
    Analyze all subset relationships and return comprehensive summary
    """
    groups = profiles_df.columns.tolist()
    subset_relationships = []
    
    # Check all pairs
    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                if is_subset_group_quiet(group_a, group_b, profiles_df):
                    subset_relationships.append((group_a, group_b))
    
    return subset_relationships, groups

def print_detailed_comparison_table(profiles_df):
    """
    Print a detailed comparison table showing all group attributes
    """
    print("📋 DETAILED GROUP COMPARISON TABLE:")
    print("="*80)
    
    # Create a nice formatted table
    for idx, attribute in enumerate(profiles_df.index):
        print(f"\n{attribute.upper()}:")
        print("-" * 50)
        for group in profiles_df.columns:
            value = str(profiles_df.loc[attribute, group])
            print(f"  {group:12}: {value}")

# Load the data
Sample_Profiles = pd.read_csv("artifacts/2025-05-31T13-26_export2.csv").set_index('Unnamed: 0').fillna("ALL")

print("🎯 COMPLETE SUBSET RELATIONSHIP ANALYSIS")
print("="*80)

# Show the data first
print_detailed_comparison_table(Sample_Profiles)

# Analyze relationships
subset_relationships, all_groups = analyze_all_subset_relationships(Sample_Profiles)

print(f"\n\n📊 SUBSET ANALYSIS RESULTS:")
print("="*80)
print(f"Total Groups Analyzed: {len(all_groups)}")
print(f"Groups: {', '.join(all_groups)}")
print()

if subset_relationships:
    print("✅ SUBSET RELATIONSHIPS FOUND:")
    print("-" * 50)
    
    for i, (subset, parent) in enumerate(subset_relationships, 1):
        print(f"{i}. {subset} ⊆ {parent}")
        print(f"   → {subset} is a subset of {parent}")
        print(f"   → {subset} can potentially be merged into {parent}")
        print()
    
    # Analysis by group
    print("📈 ANALYSIS BY GROUP:")
    print("-" * 50)
    
    # Groups that are subsets
    subset_groups = set([rel[0] for rel in subset_relationships])
    if subset_groups:
        print("🔸 Groups that are SUBSETS of other groups:")
        for group in subset_groups:
            parents = [rel[1] for rel in subset_relationships if rel[0] == group]
            print(f"   • {group} → subset of: {', '.join(parents)}")
        print()
    
    # Groups that contain others
    parent_groups = set([rel[1] for rel in subset_relationships])
    if parent_groups:
        print("🔹 Groups that CONTAIN other groups:")
        for parent in parent_groups:
            subsets = [rel[0] for rel in subset_relationships if rel[1] == parent]
            print(f"   • {parent} → contains: {', '.join(subsets)}")
        print()
    
    # Independent groups
    independent_groups = [group for group in all_groups if group not in subset_groups]
    if independent_groups:
        print("🔸 Groups that are INDEPENDENT (not subsets of any other):")
        for group in independent_groups:
            print(f"   • {group}")
        print()
    
    print("💡 RECOMMENDATIONS:")
    print("-" * 50)
    print("Based on subset relationships, consider:")
    for subset, parent in subset_relationships:
        print(f"   • Merge {subset} into {parent} to reduce redundancy")
    
else:
    print("❌ NO SUBSET RELATIONSHIPS FOUND")
    print("-" * 50)
    print("• All groups are independent")
    print("• No groups can be merged based on subset logic")
    print("• Each group has unique targeting criteria")

print(f"\n{'='*80}")
print("Analysis Complete! 🎉")
