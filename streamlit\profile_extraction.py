import streamlit as st
import numpy as np
import pandas as pd
import datetime
# import gender_guesser.detector as gender
from kneed import <PERSON><PERSON><PERSON>ocator 
# d = gender.Detector()
from sklearn.cluster import AgglomerativeClustering
from sklearn.decomposition import PCA
from yellowbrick.cluster import KElbowVisualizer
import sys
import warnings
if not sys.warnoptions:
    warnings.simplefilter("ignore")
import plotly.express as px
from sklearn.cluster import KMeans
import seaborn as sns
import matplotlib.pyplot as plt
import ast 
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import StandardScaler

st.set_page_config(layout='wide')

#---------------------------------------------Functions --------------------------------------------------------------------
def calculate_match(row, profile):
    total_fields = len(profile) - 1 
    matches = 0
    
    for field in profile:
        if field == 'Profile':
            continue
        if (profile[field] != '' and row[field]!='') and row[field] == profile[field]:
            matches += 1
    return (matches / total_fields) * 100

def multi_calculate_match(row, profile):
    total_fields = len(profile) - 1  
    matches = 0
    for field in profile:
        if field == 'Profile':
            continue

        if profile.get(field) and row.get(field):
            if isinstance(profile[field], str) and ',' in profile[field]:
                profile_values = [val.strip() for val in profile[field].split(', ')]
            else:
                profile_values = [profile[field]] 

            if str(row[field]) in profile_values:
                matches += 1
    return (matches / total_fields) * 100

# def age(dob):
#     try:
#         born_year = pd.to_datetime(dob).date().year
#         current_year = datetime.datetime.now().year
#         age = current_year - born_year
#         return age
#     except:
#         return None

# def predict_gender(row):
#     if pd.isna(row['GenderNew']) : 
#         for name_column in ['FirstName', 'MiddleName', 'Surname']:
#             name = row.get(name_column)
#             if pd.notna(name) and len(name)>1: 
#                 predicted_gender = d.get_gender(name)
#                 if predicted_gender in ['male', 'female']:
#                     return predicted_gender
#     return row['GenderTemp']

# def preprocss(df):
    
#     #------------------------------Age-------------------------------------------
#     age_df = df[['Age',"Merged_Age_Range","Age_Merged","Age_S4614","Age_Range_S4614","DOB_Formatted","DOB_Merged"]]
#     age_df['AgeNew'] = age_df['DOB_Formatted'].apply(age)
#     age_ranges = {
#     "0-4": (0, 4),
#     "5-9": (5, 9),
#     "10-14": (10, 14),
#     "15-19": (15, 19),
#     "20-24": (20, 24),
#     "25-29": (25, 29),
#     "30-34": (30, 34),
#     "35-39": (35, 39),
#     "40-44": (40, 44),
#     "45-49": (45, 49),
#     "50-54": (50, 54),
#     "55-59": (55, 59),
#     "60-64": (60, 64),
#     "65-69": (65, 69),
#     "70-74": (70, 74),
#     "75-79": (75, 79),
#     "80-84": (80, 84),
#     "85-89": (85, 89),
#     "90-94": (90, 94),
#     "Over 95": (95, float('inf'))}
#     def get_age_range(age):
#         for label, (lower, upper) in age_ranges.items():
#             if lower <= age <= upper:
#                 return label
#         return "" 
    
#     age_df.loc[
#         (age_df['DOB_Formatted'].isna()) & (age_df['DOB_Merged'].notna()),
#         'AgeNew'
#     ] = age_df.loc[
#         (age_df['DOB_Formatted'].isna()) & (age_df['DOB_Merged'].notna()),
#         'DOB_Merged'
#     ].apply(age)

#     age_df['AgeRangeNew'] = age_df['AgeNew'].apply(get_age_range)

#     age_df.loc[
#         (age_df['AgeRangeNew'] == 'Unknown') & (age_df['Merged_Age_Range'].notna()),
#         'AgeRangeNew'
#     ] = age_df['Merged_Age_Range']
#     age_df.loc[
#         (age_df['AgeRangeNew'] == 'Unknown') & (age_df['Age_Range_S4614'].notna()),
#         'AgeRangeNew'
#     ] = age_df['Merged_Age_Range']
    

    
#     #------------------------------Gender-------------------------------------------
#     gender_df = df[["FirstName","MiddleName","Surname",'Gender', "GenderTemp"]]
#     gender_df['GenderNew'] = np.nan
#     gender_df.loc[
#         ((gender_df['Gender'].notna()) & (gender_df['Gender'] != "_")),
#         'GenderNew'
#     ] = gender_df['GenderTemp']

#     gender_df.loc[
#         ((gender_df['GenderTemp'].notna()) & (gender_df['GenderTemp'] != "_")),
#         'GenderNew'
#     ] = gender_df['Gender']

#     gender_df['GenderNew'] = gender_df.apply(predict_gender, axis=1)
#     gender_df['GenderNew'] = gender_df['GenderNew'].fillna(gender_df['GenderTemp'])
#     gender_df['GenderNew'] = gender_df['GenderNew'].replace({'F':'female', 'M':'male'})

#     #------------------------------HomeOwner-------------------------------------------
#     home_owner_df = df[["HomeOwner","Homeowner_Merged"]]
#     gender_df['HomeOwnerNew'] = np.nan

#     home_owner_df.loc[
#         (home_owner_df['HomeOwner'].notna()),
#         'HomeOwnerNew'
#     ] = home_owner_df['HomeOwner']

#     home_owner_df.loc[
#         ((home_owner_df['Homeowner_Merged'].notna())),
#         'HomeOwnerNew'
#     ] = home_owner_df['Homeowner_Merged']

#     home_owner_df['HomeOwnerNew'] = home_owner_df['HomeOwnerNew'].fillna("renters")
#     home_owner_df['HomeOwnerNew'] = home_owner_df['HomeOwnerNew'].replace({'Y':"HomeOwner"})
    
#     # Create analysis dataframe with only the needed columns
#     analysis_df = pd.concat(
#         [
#             age_df[['AgeRangeNew']], 
#             gender_df[['GenderNew']], 
#             df[['IncomeRange_S4614']], 
#             home_owner_df[['HomeOwnerNew']]
#         ], 
#         axis=1
#     )

#     # Rename columns
#     analysis_df = analysis_df.rename(columns={
#         'AgeRangeNew': 'Age',
#         'GenderNew': 'Gender',
#         'IncomeRange_S4614': 'Income', 
#         'HomeOwnerNew': 'HomeOwner'
#     })

#     return analysis_df

def normalization(df, cols_encode):
    encoded_df = df.copy()
    
    income_order = {'': 9, 'Under $20,799': 1, '$20,800 - $41,599': 2, '$41,600 - $64,999':3, '$65,000 - $77,999':4, '$78,000 - $103,999':5, '$104,000 - $155,999':6, '$156,000+':7}
    age_order = {
        '':1,'0-4':2,'5-9': 3,'10-14': 4,'15-19':5,'20-24': 6,'25-29': 7,'30-34': 8,'35-39': 9,'40-44': 10,'45-49': 11,'50-54': 12,'55-59': 13,'60-64': 14,'65-69': 15,
        '70-74': 16,'75-79': 17,'80-84': 18,'>84': 19, '85-89': 20,'90-94': 21,'Over 95':22}
    home_owner_order = {'':1,'renters':2,'HomeOwner':3}
    gender_order = {'male':3,'female':2,'':1}

    df_encoded = encoded_df.copy()

    if 'Income' in cols_encode:
        df_encoded['Income'] = encoded_df['Income'].map(income_order)
    if 'Age' in cols_encode:
        df_encoded['Age'] = encoded_df['Age'].map(age_order)
    if 'Gender' in cols_encode:
        df_encoded['Gender'] = encoded_df['Gender'].map(gender_order)
    if 'HomeOwner' in cols_encode:
        df_encoded['HomeOwner'] = encoded_df['HomeOwner'].map(home_owner_order)

    df_encoded = df_encoded[cols_encode]
    
    return df_encoded

def Dimensional_Reduction(scaled_df):
    pca = PCA(n_components=3)
    pca.fit(scaled_df)
    PCA_df = pd.DataFrame(pca.transform(scaled_df), columns=(["col1","col2", "col3"]))
    
    return PCA_df

def Threshold(column, breakdowns_all):
    test1 = breakdowns_all[breakdowns_all['Category']==column]
    relative_cols = [col for col in test1.columns if col.endswith('_Relative_Percentage')]
    val = test1[relative_cols].values.tolist()  
    thresold = np.mean(val)+0.5*np.std(val)
    return thresold

def remove_empty_elements_from_string(lst_str):
    try:
        lst = ast.literal_eval(lst_str)
        return str([item for item in lst if not "empty_" in item])
    except (ValueError, SyntaxError):
        return lst_str
#------------------------------------------------------ End of Functions --------------------------------------------------

# Set default values
is_toggled = False
cols_encode = ['Income','Gender','Age','HomeOwner']

def lookalike_profile_extraction(data): 
    cols_encode = ['Income','Gender','Age','HomeOwner']

    dilworth = data
    cols_encode = [col for col in cols_encode if col in dilworth.columns]
    # Normalize data
    scaled_df = normalization(dilworth, cols_encode=cols_encode)
    PCA_df = Dimensional_Reduction(scaled_df)
    
    # Find optimal number of clusters
    inertia_list = []
    cluster_range = range(2, 10)  

    for n_clusters in cluster_range:
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans.fit(PCA_df)
        
        inertia_list.append(kmeans.inertia_)

    kl = KneeLocator(cluster_range, inertia_list, curve="convex", direction="decreasing")
    optimal_clusters = kl.elbow

    # Perform clustering
    AC = AgglomerativeClustering(n_clusters=optimal_clusters)
    yhat_AC = AC.fit_predict(PCA_df)
    PCA_df["Clusters"] = yhat_AC+1

    dilworth_preprocessed = data
    dilworth_preprocessed["Clusters"] = yhat_AC+1

    # Reorder clusters by size
    cluster_counts = dilworth_preprocessed['Clusters'].value_counts()
    cluster_mapping = {old_label: idx + 1 for idx, old_label in enumerate(cluster_counts.index)}
    dilworth_preprocessed['Clusters'] = dilworth_preprocessed['Clusters'].map(cluster_mapping)
    PCA_df['Clusters'] = PCA_df['Clusters'].map(cluster_mapping)

    # Create 3D visualization
    fig = px.scatter_3d(
        PCA_df, 
        x="col1", 
        y="col2", 
        z="col3", 
        color='Clusters'
    )
    fig.update_layout(coloraxis_colorbar_title="Groups")

    # Define columns to analyze
    cols_del = ['Income','Gender','Age','HomeOwner']

    # Calculate sample breakdown
    dfs = []
    for col in cols_encode:
        count_df = pd.DataFrame(dilworth_preprocessed[col].value_counts())
        percent_df = pd.DataFrame(dilworth_preprocessed[col].value_counts(normalize=True) * 100)
        combined_df = pd.concat([count_df, percent_df], axis=1)
        combined_df['Category'] = col
        combined_df.columns = ['Count', 'Percentage','Category']
        combined_df.index = combined_df.index.map(lambda x: f"empty_{col}" if x == "" else x)
        dfs.append(combined_df)

    final_df = pd.concat(dfs, axis=0)
    final_df.columns = ['Sample_Count', 'Sample_Percentage','Category']

    # Calculate breakdown by cluster
    dfs_all = []
    for cluster in sorted(dilworth_preprocessed.Clusters.unique().tolist()):
        dfs = []
        # profile_df = dilworth_preprocessed[dilworth_preprocessed['Clusters'] == cluster][cols_del]
        profile_df = dilworth_preprocessed[dilworth_preprocessed['Clusters'] == cluster][cols_encode]


        # for col in cols_del:
        for col in cols_encode:
            count_df = pd.DataFrame(profile_df[col].value_counts())
            count_df.columns = [f'Group_{cluster}_Count']
            count_df.index = count_df.index.map(lambda x: f"empty_{col}" if x == "" else x)
            dfs.append(count_df)
        profile_df_values = pd.concat(dfs, axis=0)
        profile_df_values = profile_df_values.reindex(final_df.index, fill_value=0)
        dfs_all.append(profile_df_values)
        
    dfs_all = pd.concat(dfs_all, axis=1)

    # Combine all breakdowns
    breakdowns_all = pd.concat([final_df, dfs_all], axis=1)
    for i in range(1, optimal_clusters+1):
        breakdowns_all[f"Group_{i}_Percentage"] = (breakdowns_all[f"Group_{i}_Count"] / breakdowns_all['Sample_Count']) * 100
        breakdowns_all[f"Group_{i}_Relative_Percentage"] = (breakdowns_all[f"Group_{i}_Percentage"] * breakdowns_all['Sample_Percentage']) / 100
    
    # Extract results for each demographic category
    results = {}
    for i in range(1, optimal_clusters + 1):
        column = f"Group_{i}_Relative_Percentage"
        filtered = breakdowns_all.copy()
        
        # Use Income threshold for Income category, and a general threshold for others
        income_threshold = Threshold('Income', breakdowns_all)
        general_threshold = np.mean([Threshold(col, breakdowns_all) for col in cols_del if col != 'Income'])
        general_threshold = 3
        filtered = filtered[
            ((filtered["Category"] == "Income") & (filtered[column] > income_threshold)) | 
            ((filtered["Category"] != "Income") & (filtered[column] > general_threshold))]
        
        filtered = filtered.sort_values(by=column, ascending=False)
        grouped = filtered.groupby("Category").apply(lambda x: str(x.index.tolist()))
        results[f"Group_{i}"] = grouped.to_dict()
    # test_print = income_threshold.copy()
    # Create profiles dataframe
    profiles_selected = pd.DataFrame(results).fillna("")
    profiles_selected = profiles_selected.applymap(remove_empty_elements_from_string)
    profiles_selected = profiles_selected.apply(lambda x: x.replace(r"[\[\]']", '', regex=True))
    profiles = profiles_selected

    # Add cluster percentages
    cluster_percentages = np.ceil(dilworth_preprocessed['Clusters'].value_counts(normalize=True) * 100)
    cluster_percentages_df = cluster_percentages.to_frame().T  
    cluster_percentages_df.index = ["Group_percentage"]    
    cluster_percentages_df.columns = [f'Group_{i}' for i in cluster_percentages.index]
    profiles_selected = pd.concat([profiles_selected, cluster_percentages_df], axis=0).T
    profiles_selected = profiles_selected.T

    # Format age ranges
    for i in range(1, optimal_clusters + 1):
        ranges = profiles_selected.loc['Age', f'Group_{i}']
        ranges = ", ".join([r for r in ranges.split(", ") if r != 'Over 95'])
        
        if ranges != "": 
            value_ranges = ranges.split(', ')
            values = [int(r.split('-')[0]) for r in value_ranges]
            lowest_value = min(values)
            highest_value = max([int(r.split('-')[1]) for r in value_ranges])
            age_range = (f'{lowest_value} - {highest_value}')
            profiles_selected.loc['Age', f'Group_{i}'] = age_range
    
    # Format income ranges
    import re
    def replace_with_k_format(ranges):
        def convert_match(match):
            if match.group(2):  # Range case: '70,001 - 100,000'
                start = int(match.group(1).replace(',', '')) // 1000
                end = int(match.group(2).replace(',', '')) // 1000
                return f"{start}K - {end}K"
            else:  # Single case with '+': '156,000+'
                value = int(match.group(1).replace(',', '')) // 1000
                return f"{value}K"

        return re.sub(
            r'(\d{1,3}(?:,\d{3})*)(?: - (\d{1,3}(?:,\d{3})*))?',
            convert_match,
            ranges
        )
        
    for i in range(1, optimal_clusters + 1):
        income_ranges = profiles_selected.loc['Income', f'Group_{i}'].replace("$", '')
        modified_ranges = replace_with_k_format(income_ranges)
        profiles_selected.loc['Income', f'Group_{i}'] = modified_ranges

    # Sort income ranges
    def sort_income_ranges(value):
        if value and isinstance(value, str):
            ranges = value.split(", ")
            sorted_ranges = sorted(ranges, key=lambda x: int(''.join(filter(str.isdigit, x.split("K")[0].replace("M", "000").replace("k", "000").replace(" ", "")))))
            return ", ".join(sorted_ranges)
        return value

    for idx in ['Income']:
        if idx in profiles_selected.index:
            for col in profiles_selected.columns:
                if idx == 'Income':
                    profiles_selected.loc[idx, col] = sort_income_ranges(profiles_selected.loc[idx, col])

    # Rename columns for better readability
    cols_rename_new = {'Income':'Income', 'HomeOwner':'HomeOwner', 'Gender':'Gender', 'Age':'Age'}
    profiles_selected = profiles_selected.rename(cols_rename_new)
    
    # Create test2_breakdown for weights
    test2 = breakdowns_all[['Category'] + [f'Group_{i}_Relative_Percentage' for i in range(1, optimal_clusters+1)]]
    test2_breakdown = test2.copy()

    return profiles_selected, test2_breakdown, profiles
    return test_print,test2_breakdown
