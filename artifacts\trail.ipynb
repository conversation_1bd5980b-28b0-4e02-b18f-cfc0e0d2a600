import pandas as pd

Sample_Profiles = pd.read_csv("2025-05-31T13-26_export.csv").set_index('Unnamed: 0').fillna("ALL")
profiles = pd.read_csv("2025-05-31T13-27_export (1).csv").set_index('Unnamed: 0')
weights_for_profiles = pd.read_csv("2025-05-31T13-27_export.csv").set_index('Unnamed: 0')
Sample_Profiles

def is_subset(groupA, groupB):
    for attr in groupA:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        if not groupA[attr].issubset(groupB[attr]):
            return False
    return True

profiles.fillna("ALL")

# Function to check if one group is a subset of another
def is_subset(groupA, groupB):
    # Fill NA values with "ALL" to indicate all values are acceptable
    groupA = groupA.fillna("ALL")
    groupB = groupB.fillna("ALL")
    
    for attr in groupA.index:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        # Convert string values to sets for comparison
        set_A = set(str(groupA[attr]).split(', '))
        set_B = set(str(groupB[attr]).split(', '))
        if not set_A.issubset(set_B):
            return False
    return True

# Function to merge overlapping groups
def merge_overlapping_groups(profiles, weights_for_profiles):
    # Create a copy to avoid modifying the original
    merged_profiles = profiles.copy()
    merged_weights = weights_for_profiles.copy()
    
    # Identify groups to merge
    groups_to_merge = {}
    group_cols = [col for col in profiles.columns if col.startswith('Group_')]
    
    # Check each pair of groups
    for i, group1 in enumerate(group_cols):
        for group2 in group_cols[i+1:]:
            if is_subset(profiles[group1], profiles[group2]):
                if group2 not in groups_to_merge:
                    groups_to_merge[group2] = group1
            elif is_subset(profiles[group2], profiles[group1]):
                if group1 not in groups_to_merge:
                    groups_to_merge[group1] = group2
    
    # Merge the weights for overlapping groups
    for subset_group, parent_group in groups_to_merge.items():
        # Add weights from subset to parent
        for category in weights_for_profiles.index:
            weight_cols = [col for col in weights_for_profiles.columns 
                          if col.startswith(parent_group) or col.startswith(subset_group)]
            
            for weight_col in weight_cols:
                if weight_col.startswith(parent_group):
                    # Combine weights (you may need a different strategy)
                    subset_col = weight_col.replace(parent_group, subset_group)
                    if subset_col in weights_for_profiles.columns:
                        merged_weights.loc[category, weight_col] += merged_weights.loc[category, subset_col]
        
        # Remove the subset group from profiles and weights
        merged_profiles = merged_profiles.drop(columns=[subset_group])
        subset_weight_cols = [col for col in merged_weights.columns if col.startswith(subset_group)]
        merged_weights = merged_weights.drop(columns=subset_weight_cols)
    
    # Rename remaining groups to be sequential
    new_group_mapping = {}
    for i, group in enumerate(sorted([col for col in merged_profiles.columns if col.startswith('Group_')])):
        new_name = f"Group_{i+1}"
        new_group_mapping[group] = new_name
    
    # Rename columns in profiles and weights
    merged_profiles = merged_profiles.rename(columns=new_group_mapping)
    
    for old_name, new_name in new_group_mapping.items():
        weight_cols = [col for col in merged_weights.columns if col.startswith(old_name)]
        weight_rename = {col: col.replace(old_name, new_name) for col in weight_cols}
        merged_weights = merged_weights.rename(columns=weight_rename)
    
    return merged_profiles, merged_weights

# Apply the function to your data
unique_profiles, unique_weights = merge_overlapping_groups(profiles, weights_for_profiles)

# Display the results
print("Original profiles:")
display(profiles)
print("\nUnique profiles:")
display(unique_profiles)

unique_weights

weights_for_profiles

# Function to check if one group is a subset of another
def is_subset(groupA, groupB):
    # Fill NA values with "ALL" to indicate all values are acceptable
    groupA = groupA.fillna("ALL")
    groupB = groupB.fillna("ALL")
    
    for attr in groupA.index:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        # Convert string values to sets for comparison
        set_A = set(str(groupA[attr]).split(', '))
        set_B = set(str(groupB[attr]).split(', '))
        if not set_A.issubset(set_B):
            return False
    return True

# Function to merge specific overlapping groups
def merge_specific_groups(profiles, weights_for_profiles):
    # Create a copy to avoid modifying the original
    merged_profiles = profiles.copy()
    merged_weights = weights_for_profiles.copy()
    
    # Specific groups to check
    subset_group = 'Group_3'
    parent_group = 'Group_1'
    
    # Check if Group_3 is a subset of Group_1
    if subset_group in profiles.columns and parent_group in profiles.columns:
        if is_subset(profiles[subset_group], profiles[parent_group]):
            # Add weights from Group_3 to Group_1
            for category in weights_for_profiles.index:
                subset_cols = [col for col in weights_for_profiles.columns if col.startswith(subset_group)]
                parent_cols = [col for col in weights_for_profiles.columns if col.startswith(parent_group)]
                
                for subset_col in subset_cols:
                    # Find corresponding parent column
                    parent_col = subset_col.replace(subset_group, parent_group)
                    if parent_col in parent_cols:
                        merged_weights.loc[category, parent_col] += merged_weights.loc[category, subset_col]
            
            # Remove Group_3 from profiles and weights
            merged_profiles = merged_profiles.drop(columns=[subset_group])
            subset_weight_cols = [col for col in merged_weights.columns if col.startswith(subset_group)]
            merged_weights = merged_weights.drop(columns=subset_weight_cols)
    
    return merged_profiles, merged_weights

# Apply the function to your data
unique_profiles, unique_weights = merge_specific_groups(profiles, weights_for_profiles)

import pandas as pd
import numpy as np

# Load the data for subset analysis
Sample_Profiles = pd.read_csv("artifacts/2025-05-31T13-26_export.csv").set_index('Unnamed: 0').fillna("ALL")

# Load the actual data for merging
profiles = pd.read_csv("artifacts/2025-05-31T13-27_export (1).csv").set_index('Unnamed: 0')
weights_for_profiles = pd.read_csv("artifacts/2025-05-31T13-27_export.csv").set_index('Unnamed: 0')

print("Sample Profiles DataFrame (for subset analysis):")
print(Sample_Profiles)
print("\n" + "="*50 + "\n")

print("Actual Profiles DataFrame (for merging):")
print(profiles)
print("\n" + "="*50 + "\n")

print("Weights DataFrame:")
print(weights_for_profiles.head(10))
print("\n" + "="*50 + "\n")

def is_subset_group(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b

    Args:
        group_a_name: Name of the potential subset group
        group_b_name: Name of the potential parent group
        profiles_df: DataFrame with group profiles

    Returns:
        bool: True if group_a is a subset of group_b
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False

    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]

    print(f"Checking if {group_a_name} is a subset of {group_b_name}:")
    print(f"{group_a_name} values: {group_a.to_dict()}")
    print(f"{group_b_name} values: {group_b.to_dict()}")

    # Check each attribute/index
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()

        print(f"\nAttribute '{attribute}':")
        print(f"  {group_a_name}: '{val_a}'")
        print(f"  {group_b_name}: '{val_b}'")

        # If group_b has "ALL", it contains everything, so group_a is subset
        if val_b == "ALL":
            print(f"  → {group_b_name} has 'ALL' for {attribute}, so {group_a_name} is subset")
            continue

        # If group_a has "ALL" but group_b doesn't, then group_a is NOT a subset
        if val_a == "ALL" and val_b != "ALL":
            print(f"  → {group_a_name} has 'ALL' but {group_b_name} doesn't, so NOT a subset")
            return False

        # If both have specific values, check if group_a values are contained in group_b
        if val_a != "ALL" and val_b != "ALL":
            # Split by comma and clean whitespace
            set_a = set([x.strip() for x in val_a.split(',')])
            set_b = set([x.strip() for x in val_b.split(',')])

            print(f"  → {group_a_name} set: {set_a}")
            print(f"  → {group_b_name} set: {set_b}")

            if not set_a.issubset(set_b):
                print(f"  → {set_a} is NOT a subset of {set_b}")
                return False
            else:
                print(f"  → {set_a} IS a subset of {set_b}")

    return True

def check_all_subset_relationships(profiles_df):
    """
    Check all possible subset relationships between groups
    """
    groups = profiles_df.columns.tolist()
    print(f"Available groups: {groups}")
    print("\n" + "="*60)

    subset_relationships = []

    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                print(f"\n{'='*60}")
                is_subset = is_subset_group(group_a, group_b, profiles_df)
                if is_subset:
                    subset_relationships.append((group_a, group_b))
                    print(f"\n✅ RESULT: {group_a} IS a subset of {group_b}")
                else:
                    print(f"\n❌ RESULT: {group_a} is NOT a subset of {group_b}")
                print("="*60)

    return subset_relationships

def check_specific_subset(group_a_name, group_b_name, profiles_df):
    """
    Check if a specific group is a subset of another specific group
    """
    print(f"\nSPECIFIC SUBSET CHECK: Is {group_a_name} a subset of {group_b_name}?")
    print("="*60)

    is_subset = is_subset_group(group_a_name, group_b_name, profiles_df)

    if is_subset:
        print(f"\n✅ FINAL RESULT: {group_a_name} IS a subset of {group_b_name}")
    else:
        print(f"\n❌ FINAL RESULT: {group_a_name} is NOT a subset of {group_b_name}")

    return is_subset

def merge_subset_groups(profiles_df, weights_df, subset_relationships):
    """
    Merge subset groups into their parent groups
    - Remove only subset groups (keep parent groups)
    - Average weights between subset and parent groups
    - Fill empty values in parent profiles from subset profiles
    """
    print(f"\n🔄 STARTING MERGE PROCESS...")
    print("="*60)

    if not subset_relationships:
        print("❌ No subset relationships to merge.")
        return profiles_df.copy(), weights_df.copy()

    # Create copies to work with
    merged_profiles = profiles_df.copy()
    merged_weights = weights_df.copy()

    # Track which groups to remove (only subset groups)
    groups_to_remove = set()

    print("📊 MERGING SUBSET GROUPS INTO PARENT GROUPS:")
    print("-"*60)

    for subset_group, parent_group in subset_relationships:
        if subset_group in groups_to_remove:
            print(f"⚠️  {subset_group} already processed, skipping...")
            continue

        print(f"\n🔹 Merging {subset_group} → {parent_group}:")

        # 1. Fill empty values in parent profile from subset profile
        print(f"   1. Filling empty values in {parent_group} profile...")
        filled_count = 0
        for attribute in merged_profiles.index:
            parent_val = merged_profiles.loc[attribute, parent_group]
            subset_val = merged_profiles.loc[attribute, subset_group]

            # Check if parent has empty/nan value but subset has a value
            parent_empty = pd.isna(parent_val) or str(parent_val).strip() in ['', 'nan', 'NaN']
            subset_empty = pd.isna(subset_val) or str(subset_val).strip() in ['', 'nan', 'NaN']

            if parent_empty and not subset_empty:
                merged_profiles.loc[attribute, parent_group] = subset_val
                print(f"      • {attribute}: filled '{subset_val}' from {subset_group}")
                filled_count += 1

        if filled_count == 0:
            print(f"      • No empty values to fill in {parent_group}")

        # 2. Merge weights by averaging
        print(f"   2. Averaging weights between {subset_group} and {parent_group}...")

        subset_weight_col = f"{subset_group}_Relative_Percentage"
        parent_weight_col = f"{parent_group}_Relative_Percentage"

        if subset_weight_col in merged_weights.columns and parent_weight_col in merged_weights.columns:
            weight_changes = 0
            for category in merged_weights.index:
                subset_weight = merged_weights.loc[category, subset_weight_col]
                parent_weight = merged_weights.loc[category, parent_weight_col]

                # Calculate average (handle NaN values)
                if pd.isna(subset_weight) and pd.isna(parent_weight):
                    avg_weight = np.nan
                elif pd.isna(subset_weight):
                    avg_weight = parent_weight
                elif pd.isna(parent_weight):
                    avg_weight = subset_weight
                else:
                    avg_weight = (subset_weight + parent_weight) / 2
                    if abs(avg_weight - parent_weight) > 0.01:  # Only count significant changes
                        weight_changes += 1
                        print(f"      • {category}: {parent_weight:.1f} + {subset_weight:.1f} → {avg_weight:.1f}")

                merged_weights.loc[category, parent_weight_col] = avg_weight

            if weight_changes == 0:
                print(f"      • No significant weight changes")
            else:
                print(f"      • Updated {weight_changes} weight values")

        # Mark ONLY the subset group for removal (keep parent)
        groups_to_remove.add(subset_group)
        print(f"   3. Marked {subset_group} for removal (keeping parent {parent_group})")

    # 3. Remove subset groups
    print(f"\n🗑️  REMOVING SUBSET GROUPS:")
    print("-"*40)

    for group_to_remove in groups_to_remove:
        # Remove from profiles
        if group_to_remove in merged_profiles.columns:
            merged_profiles = merged_profiles.drop(columns=[group_to_remove])
            print(f"   ✓ Removed {group_to_remove} from profiles")

        # Remove from weights
        weight_col_to_remove = f"{group_to_remove}_Relative_Percentage"
        if weight_col_to_remove in merged_weights.columns:
            merged_weights = merged_weights.drop(columns=[weight_col_to_remove])
            print(f"   ✓ Removed {weight_col_to_remove} from weights")

    return merged_profiles, merged_weights

if __name__ == "__main__":
    print("SUBSET RELATIONSHIP ANALYSIS")
    print("="*60)

    # Check all relationships
    subset_relationships = check_all_subset_relationships(Sample_Profiles)

    print(f"\n\n🎯 COMPLETE SUBSET ANALYSIS RESULTS:")
    print("="*60)

    # Show all groups
    all_groups = Sample_Profiles.columns.tolist()
    print(f"📊 Available Groups: {all_groups}")
    print()

    if subset_relationships:
        print("✅ FOUND SUBSET RELATIONSHIPS:")
        print("-" * 40)
        for subset, parent in subset_relationships:
            print(f"  🔹 {subset} is a SUBSET of {parent}")
            print(f"     → This means {subset} can potentially be merged into {parent}")

        print(f"\n📈 Total subset relationships found: {len(subset_relationships)}")

        # Show which groups are NOT subsets of any other group
        subset_groups = set([rel[0] for rel in subset_relationships])
        independent_groups = [group for group in all_groups if group not in subset_groups]

        if independent_groups:
            print(f"\n🔸 Groups that are NOT subsets of any other group:")
            for group in independent_groups:
                print(f"     • {group}")
        print("Final Profiles selection")
        Sample_Profiles_final = Sample_Profiles[independent_groups].replace("ALL", "")
        print(Sample_Profiles_final)
        
        # Show which groups contain other groups
        parent_groups = set([rel[1] for rel in subset_relationships])
        if parent_groups:
            print(f"\n🔹 Groups that CONTAIN other groups:")
            for parent in parent_groups:
                subsets = [rel[0] for rel in subset_relationships if rel[1] == parent]
                print(f"     • {parent} contains: {', '.join(subsets)}")

    else:
        print("❌ NO SUBSET RELATIONSHIPS FOUND")
        print("All groups are independent - none can be merged based on subset logic")
        print("\nThis means each group has unique targeting criteria that don't overlap")

    # PERFORM MERGING BASED ON SUBSET RELATIONSHIPS
    print(f"\n\n{'='*80}")
    print("🔄 MERGING SUBSET GROUPS")
    print("="*80)

    if subset_relationships:
        # Perform the merge using the actual data files
        merged_profiles, merged_weights = merge_subset_groups(profiles, weights_for_profiles, subset_relationships)

        # Show results
        print(f"\n📋 MERGE RESULTS:")
        print("="*60)
        print("ORIGINAL PROFILES:")
        print(profiles)
        print(f"\nMERGED PROFILES:")
        print(merged_profiles)

        print(f"\nORIGINAL WEIGHTS (first 10 rows):")
        print(weights_for_profiles.head(10))
        print(f"\nMERGED WEIGHTS (first 10 rows):")
        print(merged_weights.head(10))

        # Save results
        merged_profiles.to_csv("subset_merged_profiles.csv")
        merged_weights.to_csv("subset_merged_weights.csv")

        print(f"\n💾 RESULTS SAVED:")
        print(f"   • subset_merged_profiles.csv")
        print(f"   • subset_merged_weights.csv")

        print(f"\n🎉 FINAL SUMMARY:")
        print("="*60)
        print(f"   • Original groups: {len(profiles.columns)} → Final groups: {len(merged_profiles.columns)}")
        print(f"   • Groups removed: {set(profiles.columns) - set(merged_profiles.columns)}")
        print(f"   • Groups remaining: {list(merged_profiles.columns)}")

    else:
        print("No merging performed - no subset relationships found.")


import streamlit as st
import pandas as pd
import numpy as np
import datetime
import ast
# from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import LabelEncoder, StandardScaler
from utils import *
from profile_extraction import *
from snowflake.snowpark import Session
from data_enrich import enrich_data


# # # Set page configuration
# st.set_page_config(
#     page_title="SimilarSeek",
#     page_icon="📊",
#     layout="wide"
# )
# App title and description
st.title("SimilarSeek")
st.markdown("""
Lookalike modeling uses your customer data to find new customers similar to your best ones.\n

""")

st.markdown("Industry selected – :green[Charity]")
data = st.file_uploader("Upload your customer data file", type=["csv", "xlsx"])
if data is not None:
    if data.name.endswith('.csv'):
        data = pd.read_csv(data,encoding='latin1', nrows=100).fillna('')
    elif data.name.endswith('.xlsx'):
        # If the file is an Excel file, read the first sheet
        data = pd.read_excel(data, sheet_name=0, encoding='latin1').fillna('')
    else:
        st.error("Unsupported file format. Please upload a CSV or Excel file.")
        st.stop()
    with st.expander("Data Preview"):
        st.dataframe(data.head(3),use_container_width=True)

    session = Session.builder.getOrCreate()
    bsm_concat = session.sql("select * from code_schema.bsm_view_all;").to_pandas()

    st.markdown("""
    This application demonstrates how to enrich customer data with missing Age, Gender, Income, and HomeOwner values
    by matching records with a reference dataset using multiple levels of matching criteria.

    1. **Level 1**: FullName + DPID
    2. **Level 2**: DPID + Mobile
    3. **Level 3**: DPID + Email
    4. **Level 4**: Mobile
    5. **Level 5**: Email
    6. **Level 6**: DPID
    7. **Level 7**: Address + Postcode
    """)

    with st.spinner("Enriching data..."):
        # Enrich the data
        enriched_data = enrich_data(data, bsm_concat)

    # # Display enriched data
    # st.header("Enriched Data")
    # st.dataframe(enriched_data)

    # Display statistics
    st.header("Enrichment Statistics")

    # Calculate statistics for each field
    stats = []
    total_records = len(enriched_data)

    def filled_count(col, verified_col=None):
        filled = (enriched_data[col] != '').sum() + enriched_data[col].notna().sum() - (enriched_data[col] == '').sum()
        if verified_col and verified_col in enriched_data.columns:
            filled_verified = (enriched_data[verified_col] != '').sum()
            return filled, filled_verified
        return filled, None

    fields = [
        ("Age", "Age_Verified"),
        ("Gender", "Gender_Verified"),
        ("Income", "Income_Verified"),
        ("HomeOwner", "HomeOwner_Verified"),
        ("Phone2_Mobile", "Mobile_Verified"),
        ("EmailAddress", "EmailAddress_Verified"),
    ]

    for col, verified_col in fields:
        base_filled = (enriched_data[col] != '').sum()
        enriched_filled = (enriched_data[verified_col] != '').sum() if col in data.columns else 0
        percent = (enriched_filled / total_records * 100) if total_records else 0
        # increment = enriched_filled - base_filled
        # increment_percent = ((increment / base_filled) * 100) if base_filled else 0
        stats.append({
            "Field": col.replace("Phone2_Mobile", "Mobile").replace("EmailAddress", "Email"),
            "Total Records": total_records,
            "Filled Before": base_filled if col in data.columns else 0,
            "Filled After": (enriched_data[verified_col] != '').sum(),
            # "Enrichment (%)": f"{percent:.1f}%"
            "Enrichment (%)": f"{(enriched_data[verified_col] != '').sum() / total_records * 100:.1f}%"
        })

    stats_df = pd.DataFrame(stats)
    st.dataframe(stats_df.set_index("Field"), use_container_width=True)
        # st.write(f"Missing Email values: {email_missing}")
    # Download option for enriched data
    # csv = enriched_data.to_csv(index=False)
    # st.download_button(
    #     label="Download Enriched Data as CSV",
    #     data=csv,
    #     file_name="enriched_data.csv",
    #     mime="text/csv",
    # )
    # st.markdown("---enrichment completed---")
    # st.dataframe(enriched_data, use_container_width=True)
    population_filter_demog, weights_for_profiles,profiles = lookalike_profile_extraction(enriched_data)

    population_filter_demog = population_filter_demog.replace("","ALL")
  
    # profiles = profiles.replace({}, np.nan)
    # profiles = profiles.replace('',np.nan)

    use_index = [col for col in ['Income', 'HomeOwner', 'Gender', 'Age','Group_percentage'] if col in profiles.index]
    
    population_filter_demog = population_filter_demog.loc[use_index]
    profiles = profiles.loc[use_index]
    st.write("Sample Profiles:")
    st.dataframe( population_filter_demog, use_container_width=True)
    st.write("weights_for_profiles", weights_for_profiles)
    st.write("profiles", profiles)

    with st.spinner("Extracting lookalike profiles..."):
        row_unique_values_dict = {
            index: list(set(row.dropna().values)) for index, row in profiles.iterrows()
        }
        row_unique_values_dict = {
            key: [item.strip() for sublist in value for item in sublist.split(', ')] 
            for key, value in row_unique_values_dict.items()
        }
        row_unique_values_dict = {
            key: list(set(value)) for key, value in row_unique_values_dict.items()
        }
        row_unique_values_dict = {
            key: [item for item in value if item.strip()] for key, value in row_unique_values_dict.items()
        }
        population_filter_demog = {key: value for key, value in row_unique_values_dict.items() if key in ['Age', 'Gender', 'HomeOwner', 'Income', 'Group_percentage']}
        population = bsm_concat[
        (bsm_concat['Age1'.upper()].isin(population_filter_demog['Age'])) &
        (bsm_concat['Gender1'.upper()].isin(population_filter_demog['Gender'])) &
        (bsm_concat['HomeOwner1'.upper()].isin(population_filter_demog['HomeOwner'])) &
        (bsm_concat['Income1'.upper()].isin(population_filter_demog['Income']))
        ]
        # st.write("Population size:", population.shape[0])
        # st.write("Population preview:")
        # st.dataframe(population.head(3), use_container_width=True)


        #-------------------------
        result = {
        col: weights_for_profiles[col].to_dict()
        for col in weights_for_profiles.drop(columns=["Category"]).columns
        }
        result = {
            key.replace('_Relative_Percentage', ''): value
            for key, value in result.items()
        }
        # st.write("Weights for profiles:", result)


        for group in result.keys():  # Iterate over Group_1, Group_2, etc.
            population[group] = population.apply(
                lambda row: sum(
                    result[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
                ),
                axis=1
            )

        
        group_columns = [col for col in population.columns if col.startswith('Group_')]
        population['max_value'] = population[group_columns].max(axis=1)
        population['selected_group'] = population[group_columns].idxmax(axis=1)


        from sklearn.preprocessing import MinMaxScaler

        # Initialize the MinMaxScaler
        scaler = MinMaxScaler()

        # Select the columns to normalize
        columns_to_normalize = [
            col for col in weights_for_profiles.columns if 'Relative_Percentage' in col
        ]

        # Group by 'Category' and apply MinMaxScaler to each group
        weights_for_profiles[[f'normalized_{col}' for col in columns_to_normalize]] = (
            weights_for_profiles[columns_to_normalize]
            .div(weights_for_profiles[columns_to_normalize].sum(axis=1), axis=0)
        )

        result1 = {
        col: weights_for_profiles[col].to_dict()
        for col in weights_for_profiles.drop(columns=["Category"]).columns
        }
        result1 = {
            key.replace('_Relative_Percentage', ''): value
            for key, value in result1.items()
        }
        result1 = {
            key.replace('normalized_', ''): value
            for key, value in result1.items()
        }

        for group in result1.keys():  # Iterate over Group_1, Group_2, etc.
            population[group] = population.apply(
                lambda row: sum(
                    result1[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
                ),
                axis=1
            )

        
        population['max_value'] = population[group_columns].max(axis=1)
        population['selected_group'] = population[group_columns].idxmax(axis=1)

        #----------
        population['GIFT_CASUSE_PREFENCE_Flag'] = population['GIFT_CASUSE_PREFENCE'].apply(lambda x: 1 if x != '' else 0)
        bsm_segments_list = [
            'Segment_8DD_Festivals',
            'Segment_Facebook_Music',
            'Segment_Facebook_Music_Fantastic',
            'Travel_Domestic',
            'Travel_International',
            'Segment_8DD_Travel_Domestic_Air',
            'Segment_8DD_Travel_Domestic_Rail',
            'Segment_8DD_football_spectator',
            'Segment_8DD_tennis_spectator',
            'Segment_8DD_rugby_spectator',
            'Segment_8DD_Debit_Card',
            'Segment_8DD_Invest_Crypto',
            'Segment_8DD_Invest_Shares',
            'charity_donor_online',
            'charity_donor_facetoface'
        ]
        bsm_segments_list = [segment.upper() for segment in bsm_segments_list]

        # df2['BSM_segment_Flag'] = df2[bsm_segments_list].apply(lambda row: row.eq('Y').any(), axis=1).astype(int)
        population['BSM_segments_Flag'] = population[bsm_segments_list].apply(lambda row: row.eq('Y').sum(), axis=1)

        bsm_donor_involved = ['Charity_6Months',
        'Charity_12Months',
        'Charity_24Months',
        'Charity_24Months_More']
        bsm_donor_involved = [segment.upper() for segment in bsm_donor_involved]

        population['BSM_doner_involved_Flag'] = population[bsm_donor_involved].apply(lambda row: row.eq(1).sum(), axis=1)

        population['BSM_total_score'] = population['BSM_segments_Flag']*0.1 + population['BSM_doner_involved_Flag']*0.3 + population['max_value']*0.6

        # st.write("Population with BSM segments flag:")
        # st.dataframe(population.head(3), use_container_width=True)

    #-------------------------
        digi_merge = session.sql("select * from code_schema.digi_merge_view;").to_pandas()
        population['POSTCODE'] = population['POSTCODE'].astype(str)
        # digi_merge['postcode'] = digi_merge['postcode'].astype(str)
        digi_merge['POSTCODE'] = digi_merge['POSTCODE'].astype(str).str.replace('.0', '', regex=False)
        digi_merge['address_key'] = digi_merge[['AD1', 'SUBURB', 'STATE', 'POSTCODE']].astype(str).agg('|'.join, axis=1)
        digi_merge_sorted = digi_merge.sort_values(
            by=['address_key', 'full_score'.upper()],
            ascending=[True, False]
        )
        digi_merge_sorted = digi_merge_sorted.drop_duplicates(subset='address_key', keep='first')
        merged_df = population.merge(
            digi_merge_sorted[['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper(), 'full_score'.upper()]],
            left_on=['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper()],
            right_on=['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper()],
            how='left',
            indicator=True
        )
        merged_df['full_score'.upper()] = merged_df['full_score'.upper()].fillna(merged_df['BSM_total_score'])
        merged_df['Final_score'.upper()] = merged_df['BSM_total_score'] + merged_df['full_score'.upper()]
        merged_df_sorted = merged_df.sort_values('Final_score'.upper(), ascending=False)
        # st.write("Merged DataFrame with final scores:")
        # st.dataframe(merged_df_sorted.head(3), use_container_width=True)


    #-------------------------
        st.markdown(f"Total records:- :green[{len(merged_df_sorted)}]")
        no_of_records =st.number_input("No of records to filter", min_value=1, max_value=len(merged_df_sorted), value=10000, step=100, help="Number of records to filter based on the selected percentage")
        st.text("Higher scores reflect stronger alignment with your current customer profile, while lower scores indicate weaker similarity")
        # top_25_percent = merged_df_sorted.iloc[:int(len(merged_df_sorted) * top_percentage/100)]
        
        # Checkbox to exclude records present in enriched_data from merged_df_sorted
        exclude_existing = st.checkbox("Exclude records already in your uploaded data", value=True)
        filtered_merged_df_sorted = merged_df_sorted.copy()
        if exclude_existing:
            filtered_merged_df_sorted = filtered_merged_df_sorted[~filtered_merged_df_sorted['SOURCE_URN'].isin(enriched_data['SOURCE_URN'])]

        top_25_percent = filtered_merged_df_sorted.iloc[:no_of_records]
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['EmailAddress'.upper()] = top_25_percent['EmailAddress'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if isinstance(x, str) and len(x) > 0 else x)

        # Filter rows where DSAVerified is 'Yes' and sort by Final_score in descending order
        filtered_sorted_df = top_25_percent.sort_values(
            by=['DSAVerified'.upper(), 'Final_score'.upper()], 
            ascending=[False, False],
            key=lambda col: col.fillna('') if col.name == 'DSAVerified'.upper() else col
        )

        # st.dataframe(filtered_sorted_df[['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper(), 'Phone1_Landline'.upper(), 'Phone2_Mobile'.upper(),
                # 'EmailAddress'.upper(), 'selected_group', 'DSAVerified'.upper(), 'Final_score'.upper()]].head(10).reset_index(drop=True),use_container_width=True)
        top_25_percent = merged_df_sorted.iloc[:no_of_records]
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['EmailAddress'.upper()] = top_25_percent['EmailAddress'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if isinstance(x, str) and len(x) > 0 else x)

        # Filter rows where DSAVerified is 'Yes' and sort by Final_score in descending order
        filtered_sorted_df = top_25_percent.sort_values(
            by=['DSAVerified'.upper(), 'Final_score'.upper()], 
            ascending=[False, False],
            key=lambda col: col.fillna('') if col.name == 'DSAVerified'.upper() else col
        )


        # Write the filtered and sorted DataFrame to a CSV file
        # filtered_sorted_df.to_csv('filtered_sorted_output.csv', index=False)

        st.dataframe(filtered_sorted_df[['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper(), 'Phone1_Landline'.upper(), 'Phone2_Mobile'.upper(),
                'EmailAddress'.upper(), 'selected_group', 'DSAVerified'.upper(), 'Final_score'.upper()]].head(10).reset_index(drop=True),use_container_width=True)