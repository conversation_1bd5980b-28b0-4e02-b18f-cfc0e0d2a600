import pandas as pd

Sample_Profiles = pd.read_csv("2025-05-31T13-26_export.csv").set_index('Unnamed: 0').fillna("ALL")
profiles = pd.read_csv("2025-05-31T13-27_export (1).csv").set_index('Unnamed: 0')
weights_for_profiles = pd.read_csv("2025-05-31T13-27_export.csv").set_index('Unnamed: 0')
Sample_Profiles

def is_subset(groupA, groupB):
    for attr in groupA:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        if not groupA[attr].issubset(groupB[attr]):
            return False
    return True

profiles.fillna("ALL")

# Function to check if one group is a subset of another
def is_subset(groupA, groupB):
    # Fill NA values with "ALL" to indicate all values are acceptable
    groupA = groupA.fillna("ALL")
    groupB = groupB.fillna("ALL")
    
    for attr in groupA.index:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        # Convert string values to sets for comparison
        set_A = set(str(groupA[attr]).split(', '))
        set_B = set(str(groupB[attr]).split(', '))
        if not set_A.issubset(set_B):
            return False
    return True

# Function to merge overlapping groups
def merge_overlapping_groups(profiles, weights_for_profiles):
    # Create a copy to avoid modifying the original
    merged_profiles = profiles.copy()
    merged_weights = weights_for_profiles.copy()
    
    # Identify groups to merge
    groups_to_merge = {}
    group_cols = [col for col in profiles.columns if col.startswith('Group_')]
    
    # Check each pair of groups
    for i, group1 in enumerate(group_cols):
        for group2 in group_cols[i+1:]:
            if is_subset(profiles[group1], profiles[group2]):
                if group2 not in groups_to_merge:
                    groups_to_merge[group2] = group1
            elif is_subset(profiles[group2], profiles[group1]):
                if group1 not in groups_to_merge:
                    groups_to_merge[group1] = group2
    
    # Merge the weights for overlapping groups
    for subset_group, parent_group in groups_to_merge.items():
        # Add weights from subset to parent
        for category in weights_for_profiles.index:
            weight_cols = [col for col in weights_for_profiles.columns 
                          if col.startswith(parent_group) or col.startswith(subset_group)]
            
            for weight_col in weight_cols:
                if weight_col.startswith(parent_group):
                    # Combine weights (you may need a different strategy)
                    subset_col = weight_col.replace(parent_group, subset_group)
                    if subset_col in weights_for_profiles.columns:
                        merged_weights.loc[category, weight_col] += merged_weights.loc[category, subset_col]
        
        # Remove the subset group from profiles and weights
        merged_profiles = merged_profiles.drop(columns=[subset_group])
        subset_weight_cols = [col for col in merged_weights.columns if col.startswith(subset_group)]
        merged_weights = merged_weights.drop(columns=subset_weight_cols)
    
    # Rename remaining groups to be sequential
    new_group_mapping = {}
    for i, group in enumerate(sorted([col for col in merged_profiles.columns if col.startswith('Group_')])):
        new_name = f"Group_{i+1}"
        new_group_mapping[group] = new_name
    
    # Rename columns in profiles and weights
    merged_profiles = merged_profiles.rename(columns=new_group_mapping)
    
    for old_name, new_name in new_group_mapping.items():
        weight_cols = [col for col in merged_weights.columns if col.startswith(old_name)]
        weight_rename = {col: col.replace(old_name, new_name) for col in weight_cols}
        merged_weights = merged_weights.rename(columns=weight_rename)
    
    return merged_profiles, merged_weights

# Apply the function to your data
unique_profiles, unique_weights = merge_overlapping_groups(profiles, weights_for_profiles)

# Display the results
print("Original profiles:")
display(profiles)
print("\nUnique profiles:")
display(unique_profiles)

unique_weights

weights_for_profiles

# Function to check if one group is a subset of another
def is_subset(groupA, groupB):
    # Fill NA values with "ALL" to indicate all values are acceptable
    groupA = groupA.fillna("ALL")
    groupB = groupB.fillna("ALL")
    
    for attr in groupA.index:
        if groupA[attr] == "ALL":
            continue
        if groupB[attr] == "ALL":
            return False
        # Convert string values to sets for comparison
        set_A = set(str(groupA[attr]).split(', '))
        set_B = set(str(groupB[attr]).split(', '))
        if not set_A.issubset(set_B):
            return False
    return True

# Function to merge specific overlapping groups
def merge_specific_groups(profiles, weights_for_profiles):
    # Create a copy to avoid modifying the original
    merged_profiles = profiles.copy()
    merged_weights = weights_for_profiles.copy()
    
    # Specific groups to check
    subset_group = 'Group_3'
    parent_group = 'Group_1'
    
    # Check if Group_3 is a subset of Group_1
    if subset_group in profiles.columns and parent_group in profiles.columns:
        if is_subset(profiles[subset_group], profiles[parent_group]):
            # Add weights from Group_3 to Group_1
            for category in weights_for_profiles.index:
                subset_cols = [col for col in weights_for_profiles.columns if col.startswith(subset_group)]
                parent_cols = [col for col in weights_for_profiles.columns if col.startswith(parent_group)]
                
                for subset_col in subset_cols:
                    # Find corresponding parent column
                    parent_col = subset_col.replace(subset_group, parent_group)
                    if parent_col in parent_cols:
                        merged_weights.loc[category, parent_col] += merged_weights.loc[category, subset_col]
            
            # Remove Group_3 from profiles and weights
            merged_profiles = merged_profiles.drop(columns=[subset_group])
            subset_weight_cols = [col for col in merged_weights.columns if col.startswith(subset_group)]
            merged_weights = merged_weights.drop(columns=subset_weight_cols)
    
    return merged_profiles, merged_weights

# Apply the function to your data
unique_profiles, unique_weights = merge_specific_groups(profiles, weights_for_profiles)

