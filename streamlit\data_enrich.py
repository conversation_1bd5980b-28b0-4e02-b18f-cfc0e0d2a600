import streamlit as st
import pandas as pd
import numpy as np
import os
from homeowner_enrich import homeowner_enrichment
try:
    from snowflake.snowpark import Session
    SNOWFLAKE_AVAILABLE = True
except ImportError:
    SNOWFLAKE_AVAILABLE = False
    Session = None


# # Set page configuration
# st.set_page_config(
#     page_title="Data Enrichment Demo",
#     page_icon="📊",
#     layout="wide"
# )

# App title and description
# st.title("Data Enrichment Demo")
# st.markdown("""
# This application demonstrates how to enrich customer data with missing Age, Gender, Income, and HomeOwner values
# by matching records with a reference dataset using multiple levels of matching criteria.

# **HomeOwner Enrichment**: If your dataset doesn't have a HomeOwner column, the application will automatically
# add one and populate it with HOMEOWNER1 values from the BSM data using hierarchical matching levels:
# 1. **Level 1**: Address + Suburb + State + Postcode
# 2. **Level 2**: DPID + Phone + Email
# 3. **Level 3**: DPID + Phone
# 4. **Level 4**: DPID + Email
# """)

if SNOWFLAKE_AVAILABLE:
    session = Session.builder.getOrCreate()
else:
    session = None

# Function to load the BSM preprocessed data
@st.cache_data
def load_bsm_data():
    try:
        # Try different possible paths for the BSM data
        # possible_paths = [
        #     os.path.join('..', 'raw_data', 'external_source', 'bsm_concat.csv'),
        #     os.path.join('raw_data', 'external_source', 'bsm_concat.csv'),
        #     'bsm_concat.csv'
        # ]

        # for path in possible_paths:
        try:
            if session is not None:
                st.info(f"Trying to load BSM data from Snowflake...")
                bsm_data = session.sql("select * from code_schema.bsm_view_all;").to_pandas()
                st.success(f"Successfully loaded BSM data from Snowflake")
                return bsm_data
            else:
                st.info("Snowflake not available, using sample data")
                raise Exception("Snowflake not available")
        except Exception as e:
            st.warning(f"Could not load BSM data: {e}")

        # If we couldn't load the real data, create a sample BSM dataset for demo purposes
        st.warning("Could not load BSM data from any path. Creating sample BSM data for demo purposes.")

        # Create sample BSM data with columns that match our expected structure
        sample_bsm = pd.DataFrame({
            'Age1': ['30-34', '40-44', '50-54', '25-29', '60-64'],
            'Gender1': ['male', 'female', 'male', 'female', 'male'],
            'Income1': ['$104,000 - $155,999', '$156,000+', '$65,000 - $77,999', '$41,600 - $64,999', '$78,000 - $103,999'],
            'HomeOwner1': ['HomeOwner', 'renters', 'HomeOwner', 'renters', 'HomeOwner'],
            'ad1': ['123 Main St', '456 Oak Ave', '789 Pine Rd', '101 Elm St', '202 Maple Dr'],
            'suburb': ['SYDNEY', 'MELBOURNE', 'BRISBANE', 'PERTH', 'ADELAIDE'],
            'state': ['NSW', 'VIC', 'QLD', 'WA', 'SA'],
            'postcode': ['2000', '3000', '4000', '6000', '5000'],
            'DPID': ['12345678', '23456789', '34567890', '45678901', '56789012'],
            'Phone2_Mobile': ['0412345678', '0423456789', '0434567890', '0445678901', '0456789012'],
            'EmailAddress': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        })

        return sample_bsm

    except Exception as e:
        st.error(f"Error loading BSM data: {e}")
        return None

# # Function to enrich data with matching logic
def enrich_data(sample_df, bsm_df):
    # Create a copy of the sample dataframe to avoid modifying the original
    enriched_df = sample_df.copy()

    # Add HomeOwner column if it doesn't exist
    if 'HomeOwner' not in enriched_df.columns:
        enriched_df['HomeOwner'] = ''
        st.info("Added HomeOwner column to the dataset as it was missing.")

    if "SOURCE_URN" not in enriched_df.columns:
        enriched_df['SOURCE_URN'] = ''

    # Add verification columns
    enriched_df['Age_Verified'] = ''
    enriched_df['Gender_Verified'] = ''
    enriched_df['Income_Verified'] = ''
    enriched_df['HomeOwner_Verified'] = ''
    enriched_df['Mobile_Verified'] = ''
    enriched_df['EmailAddress_Verified'] = ''

    # Convert columns to string for matching
    for col in ['AD1', 'SUBURB', 'STATE', 'POSTCODE', 'DPID', 'PHONE2_MOBILE', 'EMAILADDRESS','DPID_MERGED']:
        if col in enriched_df.columns:
            enriched_df[col] = enriched_df[col].astype(str)
        if col in bsm_df.columns:
            bsm_df[col] = bsm_df[col].astype(str)

    # st.write('enrich initial',enriched_df.head())
    # st.write('bsm_df',bsm_df.DPID_MERGED.unique())

    for idx, row in enriched_df.iterrows():


        # Check if Age, Gender, Income, or HomeOwner is missing
        age_missing = pd.isna(row['Age']) or row['Age'] == ''
        gender_missing = pd.isna(row['Gender']) or row['Gender'] == ''
        income_missing = pd.isna(row['Income']) or row['Income'] == ''
        homeowner_missing = pd.isna(row.get('HomeOwner', '')) or row.get('HomeOwner', '') == ''
        mobile_missing = pd.isna(row['Phone2_Mobile']) or row['Phone2_Mobile'] == ''
        email_missing = pd.isna(row['EmailAddress']) or row['EmailAddress'] == ''
        urn_missing = pd.isna(row['SOURCE_URN']) or row['SOURCE_URN'] == ''
        # email_missing = pd.isna(row.get('EmailAddress', '')) or row.get('EmailAddress', '') == ''

        if not (age_missing or gender_missing or income_missing or homeowner_missing or mobile_missing or email_missing):
            continue  # Skip if all values are present

        if not (pd.isna(row.get('First_Name', ''))) and not(pd.isna(row.get('Surname', ''))) and  not (pd.isna(row.get('DPID', ''))):
           # Find the correct column names in BSM data
            First_Name_col = "GN_1_1"
            Surname_col = "SN_1_1"
            dpid_col = "DPID_MERGED"

            if First_Name_col and Surname_col and dpid_col:
                matches = bsm_df[
                    (bsm_df[First_Name_col].astype(str).str.lower() == str(row['First_Name']).lower()) &
                    (bsm_df[Surname_col].astype(str) == str(row['Surname'])) &
                    (bsm_df[dpid_col].astype(str) == str(row['DPID']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)

            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'FullName+DPID'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'FullName+DPID'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'FullName+DPID'

                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'FullName+DPID'

                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'FullName+DPID'

                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'FullName+DPID'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']
                
            continue  # Move to next row if match found

        if not (pd.isna(row.get('DPID', ''))) and not (pd.isna(row.get('Phone2_Mobile', ''))):
            # Find the correct column names in BSM data
            dpid_col = None
            phone_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
                    phone_col = col

            # Only proceed if we found all required columns
            if dpid_col and phone_col:
                matches = bsm_df[
                    (bsm_df[dpid_col].astype(str) == str(row['DPID'])) &
                    (bsm_df[phone_col].astype(str) == str(row['Phone2_Mobile']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:
                st.write("prcessing step 3")

                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+Mobile'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Mobile'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+Mobile'

                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'DPID+Mobile'
                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Mobile'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']
                continue  # Move to next row if match found

        if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('EmailAddress', '')):
            # Find the correct column names in BSM data
            dpid_col = None
            email_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col
                elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
                    email_col = col

            # Only proceed if we found all required columns
            if dpid_col and email_col:
                matches = bsm_df[
                    (bsm_df[dpid_col].astype(str) == str(row['DPID'])) &
                    (bsm_df[email_col].astype(str).lower() == str(row['EmailAddress']).lower())
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'DPID+EmailAddress'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'DPID+EmailAddress'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID+EmailAddress'

                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'DPID+EmailAddress'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+EmailAddress'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']
                continue

        if not (pd.isna(row.get('Phone2_Mobile', ''))):
            # Find the correct column names in BSM data
            phone_col = None

            for col in bsm_df.columns:
                if col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
                    phone_col = col

            # Only proceed if we found all required columns
            if phone_col:
                matches = bsm_df[
                    (bsm_df[phone_col].astype(str) == str(row['Phone2_Mobile']))
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'Mobile'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'Mobile'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'Mobile'

                if email_missing and 'EmailAddress'.upper() in matches.columns and not pd.isna(matches.iloc[0]['EmailAddress'.upper()]):
                    enriched_df.at[idx, 'EmailAddress'] = matches.iloc[0]['EmailAddress'.upper()]
                    enriched_df.at[idx, 'EmailAddress_Verified'] = 'Mobile'
                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'Mobile'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']

                continue  # Move to next row if match found

        if not pd.isna(row.get('EmailAddress', '')):
            # Find the correct column names in BSM data
            email_col = None

            for col in bsm_df.columns:
                if col.upper() in ['EMAILADDRESS', 'EMAIL']:
                    email_col = col

            # Only proceed if we found all required columns
            if dpid_col and email_col:
                matches = bsm_df[
                    (bsm_df[email_col].astype(str).lower() == str(row['EmailAddress']).lower())
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:
                if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
                    enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
                    enriched_df.at[idx, 'Age_Verified'] = 'EmailAddress'

                if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
                    enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
                    enriched_df.at[idx, 'Gender_Verified'] = 'EmailAddress'

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'EmailAddress'

                if mobile_missing and 'Phone2_Mobile'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Phone2_Mobile'.upper()]):
                    enriched_df.at[idx, 'Phone2_Mobile'] = matches.iloc[0]['Phone2_Mobile'.upper()]
                    enriched_df.at[idx, 'Mobile_Verified'] = 'EmailAddress'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'EmailAddress'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']

                continue

        if not pd.isna(row.get('DPID', '')):
            # Find the correct column names in BSM data
            dpid_col = None

            for col in bsm_df.columns:
                if col.upper() in ['DPID', 'DPID_MERGED']:
                    dpid_col = col

            # Only proceed if we found all required columns
            if dpid_col and email_col:
                matches = bsm_df[
                    (bsm_df[dpid_col].astype(str) == str(row['DPID'])) 
                ]
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'DPID'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']

                continue

        if not pd.isna(row.get('Ad1', ''))  and not pd.isna(row.get('Postcode', '')):
            # Find the correct column names in BSM data (handle case variations)
            ad1_col = None
            postcode_col = None

            for col in bsm_df.columns:
                if col.upper() in ['AD1', 'ADDRESS1']:
                    ad1_col = col
                elif col.upper() in ['POSTCODE']:
                    postcode_col = col

            # Only proceed if we found all required columns
            if ad1_col  and postcode_col:
                matches = bsm_df[
                    (bsm_df[ad1_col].astype(str).str.lower() == str(row['Ad1']).lower()) &
                    (bsm_df[postcode_col].astype(str) == str(row['Postcode']))
                ]
            
            else:
                matches = pd.DataFrame()  # Empty dataframe if columns not found

            matches['PHONE2_MOBILE'] = matches['PHONE2_MOBILE'].replace("None", np.nan)
            matches['EMAILADDRESS'] = matches['EMAILADDRESS'].replace("None", np.nan)
            if len(matches) > 0:

                if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
                    enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
                    enriched_df.at[idx, 'Income_Verified'] = 'Ad1+Postcode'

                # Check for HomeOwner column (handle different naming conventions)
                homeowner_col = None
                for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
                    if col in matches.columns:
                        homeowner_col = col
                        break

                if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
                    enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
                    enriched_df.at[idx, 'HomeOwner_Verified'] = 'Ad1+Postcode'

                if urn_missing:
                    enriched_df.at[idx, 'SOURCE_URN'] = matches.iloc[0]['SOURCE_URN']
                    
                continue  # Move to next row if match found

    return enriched_df


# def enrich_data(sample_df, bsm_df):
#     # Create a copy of the sample dataframe to avoid modifying the original
#     enriched_df = sample_df.copy()

#     # Add HomeOwner column if it doesn't exist
#     if 'HomeOwner' not in enriched_df.columns:
#         enriched_df['HomeOwner'] = ''
#         st.info("Added HomeOwner column to the dataset as it was missing.")

#     # Add verification columns
#     enriched_df['Age_Verified'] = ''
#     enriched_df['Gender_Verified'] = ''
#     enriched_df['Income_Verified'] = ''
#     enriched_df['HomeOwner_Verified'] = ''


#     # Convert columns to string for matching
#     for col in ['Ad1', 'Suburb', 'State', 'Postcode', 'DPID', 'Phone2_Mobile', 'EmailAddress']:
#         if col in enriched_df.columns:
#             enriched_df[col] = enriched_df[col].astype(str)
#         if col in bsm_df.columns:
#             bsm_df[col] = bsm_df[col].astype(str)

#     # Matching logic
#     for idx, row in enriched_df.iterrows():
#         # Check if Age, Gender, Income, or HomeOwner is missing
#         age_missing = pd.isna(row['Age']) or row['Age'] == ''
#         gender_missing = pd.isna(row['Gender']) or row['Gender'] == ''
#         income_missing = pd.isna(row['Income']) or row['Income'] == ''
#         homeowner_missing = pd.isna(row.get('HomeOwner', '')) or row.get('HomeOwner', '') == ''

#         if not (age_missing or gender_missing or income_missing or homeowner_missing):
#             continue  # Skip if all values are present

#         # Step 1: Match by Ad1, Suburb, State, Postcode, Phone2_Mobile, EmailAddress
#         if not pd.isna(row.get('Ad1', '')) and not pd.isna(row.get('Suburb', '')) and not pd.isna(row.get('State', '')) and not pd.isna(row.get('Postcode', '')):
#             # Find the correct column names in BSM data (handle case variations)
#             ad1_col = None
#             suburb_col = None
#             state_col = None
#             postcode_col = None

#             for col in bsm_df.columns:
#                 if col.upper() in ['AD1', 'ADDRESS1']:
#                     ad1_col = col
#                 elif col.upper() in ['SUBURB']:
#                     suburb_col = col
#                 elif col.upper() in ['STATE']:
#                     state_col = col
#                 elif col.upper() in ['POSTCODE']:
#                     postcode_col = col

#             # Only proceed if we found all required columns
#             if ad1_col and suburb_col and state_col and postcode_col:
#                 matches = bsm_df[
#                     (bsm_df[ad1_col].astype(str).str.lower() == str(row['Ad1']).lower()) &
#                     (bsm_df[suburb_col].astype(str).str.lower() == str(row['Suburb']).lower()) &
#                     (bsm_df[state_col].astype(str).str.lower() == str(row['State']).lower()) &
#                     (bsm_df[postcode_col].astype(str) == str(row['Postcode']))
#                 ]
#             else:
#                 matches = pd.DataFrame()  # Empty dataframe if columns not found

#             if len(matches) > 0:
#                 if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1']):
#                     enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
#                     enriched_df.at[idx, 'Age_Verified'] = 'Ad1+Suburb+State+Postcode'

#                 if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1']):
#                     enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
#                     enriched_df.at[idx, 'Gender_Verified'] = 'Ad1+Suburb+State+Postcode'

#                 if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1']):
#                     enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
#                     enriched_df.at[idx, 'Income_Verified'] = 'Ad1+Suburb+State+Postcode'

#                 # Check for HomeOwner column (handle different naming conventions)
#                 homeowner_col = None
#                 for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
#                     if col in matches.columns:
#                         homeowner_col = col
#                         break

#                 if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
#                     enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
#                     enriched_df.at[idx, 'HomeOwner_Verified'] = 'Ad1+Suburb+State+Postcode'

#                 continue  # Move to next row if match found

#         # Step 2: Match by DPID, Phone2_Mobile, EmailAddress
#         if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('Phone2_Mobile', '')) and not pd.isna(row.get('EmailAddress', '')):
#             # Find the correct column names in BSM data
#             dpid_col = None
#             phone_col = None
#             email_col = None

#             for col in bsm_df.columns:
#                 if col.upper() in ['DPID', 'DPID_MERGED']:
#                     dpid_col = col
#                 elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
#                     phone_col = col
#                 elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
#                     email_col = col

#             # Only proceed if we found all required columns
#             if dpid_col and phone_col and email_col:
#                 matches = bsm_df[
#                     (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
#                     (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower()) &
#                     (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
#                 ]
#             else:
#                 matches = pd.DataFrame()  # Empty dataframe if columns not found

#             if len(matches) > 0:
#                 if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1']):
#                     enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
#                     enriched_df.at[idx, 'Age_Verified'] = 'DPID+Phone2_Mobile+EmailAddress'

#                 if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1']):
#                     enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
#                     enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Phone2_Mobile+EmailAddress'

#                 if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1']):
#                     enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
#                     enriched_df.at[idx, 'Income_Verified'] = 'DPID+Phone2_Mobile+EmailAddress'

#                 # Check for HomeOwner column (handle different naming conventions)
#                 homeowner_col = None
#                 for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
#                     if col in matches.columns:
#                         homeowner_col = col
#                         break

#                 if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
#                     enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
#                     enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Phone2_Mobile+EmailAddress'

#                 continue  # Move to next row if match found

#         # Step 3: Match by DPID, Phone2_Mobile
#         if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('Phone2_Mobile', '')):
#             # Find the correct column names in BSM data
#             dpid_col = None
#             phone_col = None

#             for col in bsm_df.columns:
#                 if col.upper() in ['DPID', 'DPID_MERGED']:
#                     dpid_col = col
#                 elif col.upper() in ['PHONE2_MOBILE', 'PHONE_MOBILE', 'MOBILE']:
#                     phone_col = col

#             # Only proceed if we found all required columns
#             if dpid_col and phone_col:
#                 matches = bsm_df[
#                     (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
#                     (bsm_df[phone_col].astype(str).str.lower() == str(row['Phone2_Mobile']).lower())
#                 ]
#             else:
#                 matches = pd.DataFrame()  # Empty dataframe if columns not found

#             if len(matches) > 0:
#                 if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
#                     enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
#                     enriched_df.at[idx, 'Age_Verified'] = 'DPID+Phone2_Mobile'

#                 if gender_missing and 'Gender1' .upper()in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
#                     enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
#                     enriched_df.at[idx, 'Gender_Verified'] = 'DPID+Phone2_Mobile'

#                 if income_missing and 'Income1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
#                     enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
#                     enriched_df.at[idx, 'Income_Verified'] = 'DPID+Phone2_Mobile'

#                 # Check for HomeOwner column (handle different naming conventions)
#                 homeowner_col = None
#                 for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
#                     if col in matches.columns:
#                         homeowner_col = col
#                         break

#                 if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
#                     enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
#                     enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+Phone2_Mobile'

#                 continue  # Move to next row if match found

#         # Step 4: Match by DPID, EmailAddress
#         if not pd.isna(row.get('DPID', '')) and not pd.isna(row.get('EmailAddress', '')):
#             # Find the correct column names in BSM data
#             dpid_col = None
#             email_col = None

#             for col in bsm_df.columns:
#                 if col.upper() in ['DPID', 'DPID_MERGED']:
#                     dpid_col = col
#                 elif col.upper() in ['EMAILADDRESS', 'EMAIL']:
#                     email_col = col

#             # Only proceed if we found all required columns
#             if dpid_col and email_col:
#                 matches = bsm_df[
#                     (bsm_df[dpid_col].astype(str).str.lower() == str(row['DPID']).lower()) &
#                     (bsm_df[email_col].astype(str) == str(row['EmailAddress']))
#                 ]
#             else:
#                 matches = pd.DataFrame()  # Empty dataframe if columns not found

#             if len(matches) > 0:
#                 if age_missing and 'Age1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Age1'.upper()]):
#                     enriched_df.at[idx, 'Age'] = matches.iloc[0]['Age1'.upper()]
#                     enriched_df.at[idx, 'Age_Verified'] = 'DPID+EmailAddress'

#                 if gender_missing and 'Gender1'.upper() in matches.columns and not pd.isna(matches.iloc[0]['Gender1'.upper()]):
#                     enriched_df.at[idx, 'Gender'] = matches.iloc[0]['Gender1'.upper()]
#                     enriched_df.at[idx, 'Gender_Verified'] = 'DPID+EmailAddress'

#                 if income_missing and 'Income1'.upper()in matches.columns and not pd.isna(matches.iloc[0]['Income1'.upper()]):
#                     enriched_df.at[idx, 'Income'] = matches.iloc[0]['Income1'.upper()]
#                     enriched_df.at[idx, 'Income_Verified'] = 'DPID+EmailAddress'

#                 # Check for HomeOwner column (handle different naming conventions)
#                 homeowner_col = None
#                 for col in ['HomeOwner1', 'HOMEOWNER1', 'HomeOwner', 'HOMEOWNER']:
#                     if col in matches.columns:
#                         homeowner_col = col
#                         break

#                 if homeowner_missing and homeowner_col and not pd.isna(matches.iloc[0][homeowner_col]):
#                     enriched_df.at[idx, 'HomeOwner'] = matches.iloc[0][homeowner_col]
#                     enriched_df.at[idx, 'HomeOwner_Verified'] = 'DPID+EmailAddress'

#     return enriched_df

# Main application
def main():
    # Load BSM data
    bsm_data = load_bsm_data()

    # File upload section
    st.header("Upload Customer Data")

    # # Option to use default file
    # use_default = st.checkbox("Use default sample file", value=True)

    # if use_default:
    #     # Try different possible paths for the default file
    #     # possible_paths = [
    #     #     os.path.join('..', 'raw_data', 'external_source', 'customers data(in).csv'),
    #     #     os.path.join('raw_data', 'external_source', 'customers data(in).csv'),
    #     #     'customers data(in).csv'
    #     # ]

    #     sample_data = None
    #     # for path in possible_paths:
    #     try:
    #         st.info(f"Trying to load default file from: {path}")
    #         # if os.path.exists(path):
    #             sample_data = pd.read_csv(path, encoding='latin1')
    #             st.success(f"Successfully loaded default file from: {path}")
    #             break
    #     except Exception as e:
    #         st.warning(f"Could not load default file from {path}: {e}")

    #     if sample_data is None:
    #         st.warning("Could not load default file from any path. Creating sample data for demo purposes.")

    #         # Create sample customer data
    #         sample_data = pd.DataFrame({
    #             'FirstName': ['John', 'Jane', 'Michael', 'Emily', 'David'],
    #             'Surname': ['Smith', 'Doe', 'Johnson', 'Brown', 'Wilson'],
    #             'Income': ['$104,000 - $155,999', '', '$65,000 - $77,999', '', '$78,000 - $103,999'],
    #             'Gender': ['male', 'female', '', 'female', ''],
    #             'Age': ['40-44', '', '50-54', '', '35-39'],
    #             'Gnaf_Pid': ['GANSW123456789', 'GAVIC987654321', 'GAQLD123789456', 'GAWA456123789', 'GASA789456123'],
    #             'Ad1': ['123 Main St', '456 Oak Ave', '789 Pine Rd', '101 Elm St', '202 Maple Dr'],
    #             'Suburb': ['SYDNEY', 'MELBOURNE', 'BRISBANE', 'PERTH', 'ADELAIDE'],
    #             'State': ['NSW', 'VIC', 'QLD', 'WA', 'SA'],
    #             'Postcode': ['2000', '3000', '4000', '6000', '5000'],
    #             'DPID': ['12345678', '23456789', '34567890', '45678901', '56789012'],
    #             'Phone1_Landline': ['0212345678', '0387654321', '0733219876', '0865432109', '0887654321'],
    #             'Phone2_Mobile': ['0412345678', '0423456789', '', '0445678901', ''],
    #             'EmailAddress': ['<EMAIL>', '', '<EMAIL>', '', '<EMAIL>']
    #         })
    # else:
    # File uploader
    uploaded_file = st.file_uploader("Upload your customer data file", type=["csv", "xlsx"])

    if uploaded_file is not None:
        try:
            if uploaded_file.name.endswith('.csv'):
                sample_data = pd.read_csv(uploaded_file, encoding='latin1',nrows=500)
            elif uploaded_file.name.endswith('.xlsx'):
                sample_data = pd.read_excel(uploaded_file, sheet_name=0)
            st.success(f"File uploaded successfully: {uploaded_file.name}")
        except Exception as e:
            st.error(f"Error processing uploaded file: {e}")
            sample_data = None
    else:
        sample_data = None

    #
    # Process data if available
    if sample_data is not None and bsm_data is not None:
        # Display original data
        st.header("Original Data")
        # Convert columns to numeric (integers), forcing errors to NaN
        # sample_data['Postcode'] = pd.to_numeric(sample_data['Postcode'], errors='coerce').astype(int)
        # sample_data['DPID'] = pd.to_numeric(sample_data['DPID'], errors='coerce').astype('Int64')
        # sample_data['Phone1_Landline'] = pd.to_numeric(sample_data['Phone1_Landline'], errors='coerce').astype('Int64')
        # sample_data['Phone2_Mobile'] = pd.to_numeric(sample_data['Phone2_Mobile'], errors='coerce').astype('Int64')

        st.dataframe(sample_data.head(10))

        # Enrich data
        with st.spinner("Enriching data..."):
            # home_owner_appended_sample = homeowner_enrichment(sample_data, bsm_data)
            enriched_data = enrich_data(sample_data, bsm_data)

        # # Display enriched data
        st.header("Enriched Data")
        st.dataframe(enriched_data.head(10))

        # Display statistics
        st.header("Enrichment Statistics")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            age_filled = (enriched_data['Age_Verified'] != '').sum()
            age_missing = (enriched_data['Age'] == '').sum() + enriched_data['Age'].isna().sum()
            st.metric("Age Values Filled", age_filled)
            st.write(f"Missing Age values: {age_missing}")

            # Show verification methods
            age_methods = enriched_data['Age_Verified'].value_counts().reset_index()
            if not age_methods.empty and age_methods.iloc[0, 0] != '':
                st.write("Verification Methods:")
                for idx, row in age_methods.iterrows():
                    if row['index'] != '':
                        st.write(f"- {row['index']}: {row['Age_Verified']} records")

        with col2:
            gender_filled = (enriched_data['Gender_Verified'] != '').sum()
            gender_missing = (enriched_data['Gender'] == '').sum() + enriched_data['Gender'].isna().sum()
            st.metric("Gender Values Filled", gender_filled)
            st.write(f"Missing Gender values: {gender_missing}")

            # Show verification methods
            gender_methods = enriched_data['Gender_Verified'].value_counts().reset_index()
            if not gender_methods.empty and gender_methods.iloc[0, 0] != '':
                st.write("Verification Methods:")
                for idx, row in gender_methods.iterrows():
                    if row['index'] != '':
                        st.write(f"- {row['index']}: {row['Gender_Verified']} records")

        with col3:
            income_filled = (enriched_data['Income_Verified'] != '').sum()
            income_missing = (enriched_data['Income'] == '').sum() + enriched_data['Income'].isna().sum()
            st.metric("Income Values Filled", income_filled)
            st.write(f"Missing Income values: {income_missing}")

            # Show verification methods
            income_methods = enriched_data['Income_Verified'].value_counts().reset_index()
            if not income_methods.empty and income_methods.iloc[0, 0] != '':
                st.write("Verification Methods:")
                for idx, row in income_methods.iterrows():
                    if row['index'] != '':
                        st.write(f"- {row['index']}: {row['Income_Verified']} records")

        with col4:
            homeowner_filled = (enriched_data['HomeOwner_Verified'] != '').sum()
            homeowner_missing = (enriched_data.get('HomeOwner', pd.Series(dtype='object')) == '').sum() + enriched_data.get('HomeOwner', pd.Series(dtype='object')).isna().sum()
            st.metric("HomeOwner Values Filled", homeowner_filled)
            st.write(f"Missing HomeOwner values: {homeowner_missing}")

            # # Show verification methods
            # homeowner_methods = enriched_data['HomeOwner_Verified'].value_counts().reset_index()
            # if not homeowner_methods.empty and homeowner_methods.iloc[0, 0] != '':
            #     st.write("Verification Methods:")
            #     for idx, row in homeowner_methods.iterrows():
            #         if row['index'] != '':
            #             st.write(f"- {row['index']}: {row['HomeOwner_Verified']} records")

        with col1:
            mobile_filled = (enriched_data['Mobile_Verified'] != '').sum()
            mobile_missing = (enriched_data['Phone2_Mobile'] == '').sum() + enriched_data['Phone2_Mobile'].isna().sum()
            st.metric("Mobile Values Filled", mobile_filled)
            st.write(f"Missing Mobile values: {mobile_missing}")

        with col2:
            email_filled = (enriched_data['EmailAddress_Verified'] != '').sum()
            email_missing = (enriched_data['EmailAddress'] == '').sum() + enriched_data['EmailAddress'].isna().sum()
            st.metric("Email Values Filled", email_filled)
            st.write(f"Missing Email values: {email_missing}")
        # Download option for enriched data
        csv = enriched_data.to_csv(index=False)
        st.download_button(
            label="Download Enriched Data as CSV",
            data=csv,
            file_name="enriched_data.csv",
            mime="text/csv",
        )

if __name__ == "__main__":
    main()
