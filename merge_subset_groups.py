import pandas as pd
import numpy as np

def is_subset_group_quiet(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b (without detailed output)
    A group is a subset if ALL its criteria are contained within another group
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False

    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]

    # Check each attribute/index
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()

        # Handle NaN/empty values
        val_a_empty = pd.isna(group_a[attribute]) or val_a in ['nan', '', 'NaN']
        val_b_empty = pd.isna(group_b[attribute]) or val_b in ['nan', '', 'NaN']

        # If both are empty, continue
        if val_a_empty and val_b_empty:
            continue

        # If group_b is empty but group_a has values, group_a is NOT a subset
        if val_b_empty and not val_a_empty:
            return False

        # If group_a is empty but group_b has values, group_a IS a subset (less restrictive)
        if val_a_empty and not val_b_empty:
            continue

        # If both have values, check if group_a values are contained in group_b
        if not val_a_empty and not val_b_empty:
            # Split by comma and clean whitespace
            set_a = set([x.strip() for x in val_a.split(',') if x.strip()])
            set_b = set([x.strip() for x in val_b.split(',') if x.strip()])

            # For a true subset, group_a must be smaller or equal AND contained in group_b
            if not set_a.issubset(set_b):
                return False

    return True

def find_all_subset_relationships(profiles_df):
    """
    Find all subset relationships between groups
    """
    groups = profiles_df.columns.tolist()
    subset_relationships = []

    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                if is_subset_group_quiet(group_a, group_b, profiles_df):
                    subset_relationships.append((group_a, group_b))

    return subset_relationships

def merge_subset_groups(profiles_df, weights_df):
    """
    Merge subset groups into their parent groups
    """
    print("🔍 ANALYZING SUBSET RELATIONSHIPS...")
    print("="*60)

    # Find subset relationships
    subset_relationships = find_all_subset_relationships(profiles_df)

    if not subset_relationships:
        print("❌ No subset relationships found. Returning original data.")
        return profiles_df.copy(), weights_df.copy()

    print("✅ FOUND SUBSET RELATIONSHIPS:")
    for subset, parent in subset_relationships:
        print(f"   • {subset} ⊆ {parent}")

    # Create copies to work with
    merged_profiles = profiles_df.copy()
    merged_weights = weights_df.copy()

    # Track which groups to remove
    groups_to_remove = set()

    print(f"\n🔄 MERGING PROCESS:")
    print("-"*60)

    # Process each subset relationship individually
    for subset_group, parent_group in subset_relationships:
        if subset_group in groups_to_remove:
            print(f"⚠️  {subset_group} already processed, skipping...")
            continue

        print(f"\n📊 Merging {subset_group} into {parent_group}:")

        # 1. Fill empty values in parent profile from subset profile
        print(f"   1. Filling empty values in {parent_group} profile...")
        for attribute in merged_profiles.index:
            parent_val = merged_profiles.loc[attribute, parent_group]
            subset_val = merged_profiles.loc[attribute, subset_group]

            # If parent has empty/nan value but subset has a value, use subset value
            if (pd.isna(parent_val) or str(parent_val).strip() == '' or str(parent_val) == 'nan') and \
               not (pd.isna(subset_val) or str(subset_val).strip() == '' or str(subset_val) == 'nan'):
                merged_profiles.loc[attribute, parent_group] = subset_val
                print(f"      • {attribute}: filled '{subset_val}' from {subset_group}")

        # 2. Merge weights by averaging
        print(f"   2. Averaging weights between {subset_group} and {parent_group}...")

        subset_weight_col = f"{subset_group}_Relative_Percentage"
        parent_weight_col = f"{parent_group}_Relative_Percentage"

        if subset_weight_col in merged_weights.columns and parent_weight_col in merged_weights.columns:
            # Calculate average weights
            for category in merged_weights.index:
                subset_weight = merged_weights.loc[category, subset_weight_col]
                parent_weight = merged_weights.loc[category, parent_weight_col]

                # Calculate average (handle NaN values)
                if pd.isna(subset_weight) and pd.isna(parent_weight):
                    avg_weight = np.nan
                elif pd.isna(subset_weight):
                    avg_weight = parent_weight
                elif pd.isna(parent_weight):
                    avg_weight = subset_weight
                else:
                    avg_weight = (subset_weight + parent_weight) / 2

                merged_weights.loc[category, parent_weight_col] = avg_weight

                if not pd.isna(avg_weight) and avg_weight != parent_weight:
                    print(f"      • {category}: {parent_weight:.1f} + {subset_weight:.1f} → {avg_weight:.1f}")

        # Mark ONLY the subset group for removal (keep parent)
        groups_to_remove.add(subset_group)
        print(f"   3. Marked {subset_group} for removal (keeping parent {parent_group})")

    # 3. Remove subset groups
    print(f"\n🗑️  REMOVING SUBSET GROUPS:")
    print("-"*40)

    for group_to_remove in groups_to_remove:
        # Remove from profiles
        if group_to_remove in merged_profiles.columns:
            merged_profiles = merged_profiles.drop(columns=[group_to_remove])
            print(f"   ✓ Removed {group_to_remove} from profiles")

        # Remove from weights
        weight_col_to_remove = f"{group_to_remove}_Relative_Percentage"
        if weight_col_to_remove in merged_weights.columns:
            merged_weights = merged_weights.drop(columns=[weight_col_to_remove])
            print(f"   ✓ Removed {weight_col_to_remove} from weights")

    return merged_profiles, merged_weights

def print_comparison(original_df, merged_df, title):
    """
    Print before/after comparison
    """
    print(f"\n📋 {title}:")
    print("="*80)
    print("BEFORE:")
    print(original_df)
    print(f"\nAFTER:")
    print(merged_df)
    print(f"\nGroups reduced from {len(original_df.columns)} to {len(merged_df.columns)}")

# Load the data
print("📂 LOADING DATA...")
print("="*60)

profiles = pd.read_csv("artifacts/2025-05-31T13-27_export (1).csv").set_index('Unnamed: 0')
weights_for_profiles = pd.read_csv("artifacts/2025-05-31T13-27_export.csv").set_index('Unnamed: 0')

print("✅ Data loaded successfully!")
print(f"   • Profiles shape: {profiles.shape}")
print(f"   • Weights shape: {weights_for_profiles.shape}")

# Perform the merge
print(f"\n🚀 STARTING MERGE PROCESS...")
print("="*60)

merged_profiles, merged_weights = merge_subset_groups(profiles, weights_for_profiles)

# Show results
print_comparison(profiles, merged_profiles, "PROFILES COMPARISON")
print_comparison(weights_for_profiles, merged_weights, "WEIGHTS COMPARISON")

print(f"\n🎉 MERGE COMPLETE!")
print("="*60)
print("Summary of changes:")
print(f"   • Original groups: {list(profiles.columns)}")
print(f"   • Final groups: {list(merged_profiles.columns)}")
print(f"   • Groups removed: {set(profiles.columns) - set(merged_profiles.columns)}")

# Save results
merged_profiles.to_csv("merged_profiles.csv")
merged_weights.to_csv("merged_weights.csv")
print(f"\n💾 Results saved:")
print(f"   • merged_profiles.csv")
print(f"   • merged_weights.csv")
