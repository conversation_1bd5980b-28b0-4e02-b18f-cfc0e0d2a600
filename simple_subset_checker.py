import pandas as pd

def check_subset_relationship(group_a_name, group_b_name, profiles_df):
    """
    Simple function to check if group_a is a subset of group_b
    Returns True if group_a is a subset of group_b
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        print(f"Error: One or both groups not found in data")
        return False
    
    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]
    
    print(f"Checking if {group_a_name} is a subset of {group_b_name}:")
    print("-" * 50)
    
    # Check each attribute
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()
        
        print(f"{attribute}:")
        print(f"  {group_a_name}: {val_a}")
        print(f"  {group_b_name}: {val_b}")
        
        # If group_b has "ALL", it contains everything
        if val_b == "ALL":
            print(f"  ✓ {group_b_name} accepts ALL values for {attribute}")
            continue
            
        # If group_a has "ALL" but group_b doesn't, not a subset
        if val_a == "ALL" and val_b != "ALL":
            print(f"  ✗ {group_a_name} has ALL but {group_b_name} is specific")
            return False
            
        # Compare specific values
        if val_a != "ALL" and val_b != "ALL":
            set_a = set([x.strip() for x in val_a.split(',')])
            set_b = set([x.strip() for x in val_b.split(',')])
            
            if set_a.issubset(set_b):
                print(f"  ✓ {set_a} is contained in {set_b}")
            else:
                print(f"  ✗ {set_a} is NOT contained in {set_b}")
                return False
    
    return True

# Load the data
Sample_Profiles = pd.read_csv("artifacts/2025-05-31T13-26_export2.csv").set_index('Unnamed: 0').fillna("ALL")

print("Sample Profiles Data:")
print(Sample_Profiles)
print("\n" + "="*60 + "\n")

# Check if Group_3 is a subset of Group_1
result = check_subset_relationship("Group_3", "Group_1", Sample_Profiles)

print("\n" + "="*60)
if result:
    print("🎉 CONCLUSION: Group_3 IS a subset of Group_1")
    print("This means Group_3 can be merged into Group_1 since all Group_3 criteria are covered by Group_1")
else:
    print("❌ CONCLUSION: Group_3 is NOT a subset of Group_1")
    print("Groups cannot be merged as they have different targeting criteria")
