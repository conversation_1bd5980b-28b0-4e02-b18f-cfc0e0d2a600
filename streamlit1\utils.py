import streamlit as st
import time
import re
import textdistance
from fuzzywuzzy import fuzz
import pandas as pd
# from streamlit_toggle import st_toggle_switch
import pandas as pd
# from dotenv import load_dotenv
import os
import io
from datetime import date
# from st_aggrid import AgGrid
from utils import *
from collections  import Counter
# from rapidfuzz import fuzz
from all_functions import *
# from snowflake.snowpark import Session
from snowflake.snowpark.context import get_active_session
from concurrent.futures import ThreadPoolExecutor




def country(country_prefix):

    # if country_prefix == 'au':
        col11,col22 = st.columns((0.7,0.4))
        with col11:
            with st.expander(" 🕵🏻 :red[**User Input:**]", expanded=True):
                col1,col2,col3 = st.columns((3))
                with col1:
                    st.markdown(':green[Personal Details]')
                    id_number = st.text_input('ID Number', value=country_sources[country_prefix]['id_number'])
                    first_name = st.text_input('FirstName', value=country_sources[country_prefix]['first_name'])
                    middle_name = st.text_input('MiddleName', value=country_sources[country_prefix]['middle_name'])
                    sur_name = st.text_input('SurName', value=country_sources[country_prefix]['sur_name'])
                    dob = str(st.text_input('DOB (YYYY-MM-DD)', value=country_sources[country_prefix]['dob']))

                with col2:
                    st.markdown(':green[Address Details]')
                    
                    if country_prefix in ['au','nz']:
                        address_line1 = st.text_input('Address Line 1', value=country_sources[country_prefix]['address_line1'])
                        suburb = st.text_input('Suburb', value=country_sources[country_prefix]['suburb'])
                        state = st.text_input('State', value=country_sources[country_prefix]['state'])
                        postcode = str(st.text_input('Postcode', value=country_sources[country_prefix]['postcode']))
                    if country_prefix in ['indonisia','mx']:
                        addressElement1 = st.text_input('addressElement1 (ad1)', value=country_sources[country_prefix]['addressElement1 (ad1)'])
                        addressElement2 = st.text_input('addressElement2 (sub_district)', value=country_sources[country_prefix]['addressElement2 (sub_district)'])
                        addressElement3 = st.text_input('addressElement3 (regency)', value=country_sources[country_prefix]['addressElement3 (regency)'])
                        addressElement4 = st.text_input('addressElement4 (province)', value=country_sources[country_prefix]['addressElement4 (province)'])

                with col3:
                    st.markdown(':green[Contact Information]')
                    mobile = st.text_input('Mobile', value=country_sources[country_prefix]['mobile'])
                    if mobile:
                        if not mobile.isdigit():
                            st.error("Invalid mobile format. Please enter a valid mobile number.")
                    email = st.text_input('Email Address', value=country_sources[country_prefix]['email'])
                    if email:
                        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                        if not re.match(email_pattern, email):
                            st.error("Invalid email format. Please enter a valid email address.")
                    with col22:
                        col1, col2 = st.columns(2)
                    with col1:
                        # multi_sources = st_toggle_switch(
                        #     label="Multi Sources",
                        #     key="multi_sources_toggle",
                        #     default_value=False,
                        #     label_after=True)
                        multi_sources = False
                    with col2:
                        # json_view = st_toggle_switch(
                        #     label="JSON View",
                        #     key="json_toggle",
                        #     default_value=False,
                        #     label_after=True)
                        json_view = False
                    
                    # with col3:
                        # warehouse_sizes = ["X-SMALL", "SMALL", "MEDIUM", "LARGE", "X-LARGE"]
                        # warehouse = st.selectbox("Select Warehouse", warehouse_sizes)

                    with col22:
                        # snowflake_params = dict(st.secrets["snowflake"])
                        # snowflake_params["database"] = country_sources[country_prefix]['db_path']
                        # # st.write(snowflake_params)
                        # conn = snowflake.connector.connect(**snowflake_params)
                        # cursor = conn.cursor()
                        # cursor.execute(f"USE DATABASE {snowflake_params['database']};")
                        # query = "SHOW TABLES;"
                        # cursor.execute(query)
                        # tables = cursor.fetchall()
                        # table_names = [table[1] for table in tables]
                        # session = Session.builder.getOrCreate()
                        session = get_active_session()
                        # session.sql(f"ALTER WAREHOUSE COMPUTE_WH SET WAREHOUSE_SIZE = '{warehouse}'").collect()
                        # session.use_warehouse('COMPUTE_WH')

                        # st.write(table_names)
                        # table_query = f"SELECT * FROM {table_names[0]};"
                        # df11 = pd.read_sql_query(table_query, conn)
                        # st.write(df11)
                    #     if multi_sources:
                    #             table_names = st.multiselect("Select Sources", table_names, default=table_names)
                    if 'show_dataframe' not in st.session_state:
                        st.session_state.show_dataframe = False
                    if 'remove_anonymization' not in st.session_state:
                        st.session_state.remove_anonymization = False
                    with col2:
                        remove_anonymization = st.checkbox(':orange[**Remove Anonymization**]', value=st.session_state.remove_anonymization)
                    tables = st.selectbox("Select Source", country_sources[country_prefix]['table_name'])

                    if st.button('🔍 :orange[**Verify**]'):
                        start_time = time.time()
                        st.session_state.show_dataframe = True
                        st.session_state.remove_anonymization = remove_anonymization

                        with col22:
                            # st.session_state["verify_clicked"] = True
                            multi_sources_score = []
                            multi_mobile_email_score = []
                            dfs = {}
                            single_json = {
                                "Summary": [], 
                                "Sources": {}}
                            #------
                            # for table_name in table_names:
                            # for table_name in [country_sources[country_prefix]['table_name']]:
                            for table_name in [tables]:
                            # for table_name in ['mexico_all_records']:

                                # if st.session_state["verify_clicked"]:
                                    # query_columns = f"PRAGMA table_info({table_name})"
                                    # table_info = pd.read_sql_query(query_columns, conn)
                                    # available_columns = table_info['name'].tolist()
                                    # threshold = 0.3
                                    # query = f"""
                                    #     WITH InputData AS (
                                    #         SELECT
                                    #             '{first_name}' AS first_name_input,
                                    #             '{middle_name}' AS middle_name_input,
                                    #             '{sur_name}' AS sur_name_input
                                    #     ),
                                    #     matched_records AS (
                                    #         SELECT
                                    #             resident.*,
                                    #             CASE 
                                    #                 WHEN resident.GIVEN_NAME_1 ilike InputData.first_name_input THEN 1
                                    #                 WHEN SOUNDEX(resident.GIVEN_NAME_1) = SOUNDEX(InputData.first_name_input) THEN 0.8
                                    #                 ELSE 0
                                    #             END AS first_name_score,
                                    #             CASE 
                                    #                 WHEN resident.GIVEN_NAME_2 ilike InputData.middle_name_input THEN 1
                                    #                 WHEN SOUNDEX(resident.GIVEN_NAME_2) = SOUNDEX(InputData.middle_name_input) THEN 0.8
                                    #                 ELSE 0
                                    #             END AS middle_name_score,
                                    #             CASE 
                                    #                 WHEN resident.SURNAME ilike InputData.sur_name_input THEN 1
                                    #                 WHEN SOUNDEX(resident.SURNAME) = SOUNDEX(InputData.sur_name_input) THEN 0.8
                                    #                 ELSE 0
                                    #             END AS last_name_score
                                    #         FROM
                                    #             INDONISIA_RECORDS AS resident,
                                    #             InputData
                                    #     )
                                    #     SELECT 
                                    #         *           
                                    #     FROM 
                                    #         matched_records
                                    #     WHERE 
                                    #         first_name_score >= {threshold}
                                    #         OR middle_name_score >= {threshold}
                                    #         OR last_name_score >= {threshold}
                                    #     ORDER BY first_name_score + middle_name_score + last_name_score DESC;
                                    # """
                                    # df = pd.read_sql_query(query, conn)

                                    first_name_parts = first_name.strip().lower().split()  
                                    middle_name_parts = middle_name.strip().lower().split()  
                                    sur_name_parts = sur_name.strip().lower().split()
                                    
                                    
                                    """
                                    # Create the WHERE clauses dynamically for each column
                                    if first_name.strip():
                                        first_name_clause = " OR ".join([f"LOWER(GIVEN_NAME_1) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_2) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_3) ILIKE '%{part}%' OR LOWER(SURNAME) ILIKE '%{part}%'" for part in first_name_parts])
                                    else:
                                        first_name_clause = ""  # Leave it empty if no first name is provided
                                    if middle_name.strip():
                                        # middle_name_clause = " OR ".join([f"(GIVEN_NAME_2 ILIKE '%{part}%' OR GIVEN_NAME_3 ILIKE '%{part}%')" for part in middle_name_parts])
                                        middle_name_clause = " OR ".join([f"LOWER(GIVEN_NAME_1) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_2) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_3) ILIKE '%{part}%' OR LOWER(SURNAME) ILIKE '%{part}%'" for part in middle_name_parts])

                                    else:
                                        middle_name_clause = ""  # Leave it empty if no middle name is provided

                                    if sur_name.strip():
                                        #  sur_name_clause = " OR ".join([f"LOWER(GIVEN_NAME_1) ILIKE '%{part}%' OR LOWER(SURNAME) ILIKE '%{part}%'" for part in sur_name_parts])
                                        sur_name_clause = " OR ".join([f"LOWER(GIVEN_NAME_1) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_2) ILIKE '%{part}%' OR LOWER(GIVEN_NAME_3) ILIKE '%{part}%' OR LOWER(SURNAME) ILIKE '%{part}%'" for part in sur_name_parts])
                                    else:
                                        sur_name_clause = ""  # Leave it empty if no surname is provided

                                    # Combine all clauses using AND
                                    # where_clause = f"({first_name_clause}) OR ({middle_name_clause}) OR ({sur_name_clause})"
                                    
                                    st.write('first_name_clause',first_name_clause)
                                    st.write('middle_name_clause',middle_name_clause)
                                    st.write('sur_name_clause',sur_name_clause)

                                    clauses = [clause for clause in [first_name_clause, middle_name_clause, sur_name_clause] if clause]
                                    where_clause = " AND ".join(f"({clause})" for clause in clauses)
                                    """
                                    # where_clause = f"(FULL_NAME) ILIKE '%{first_name}%' OR (FULL_NAME) ILIKE '%{middle_name}%' OR (FULL_NAME) ILIKE '%{sur_name}%'"
                                    '''
                                    query = f"""
                                    SELECT * FROM INDONISIA_RECORDS_ALL
                                    WHERE (FULL_NAME) ILIKE '{first_name}%'
                                    OR
                                     (FULL_NAME) ILIKE '{middle_name}%'
                                    OR
                                     (FULL_NAME) ILIKE '{sur_name}%'
                                    """
                                    '''

# ---------------------------------------------- Backup Query ------------------------------------------------
                                    first_name_condition = build_match_conditions(first_name.upper(), 'GIVEN_NAME_1','FULL_NAME') if first_name else "0"
                                    middle_name_condition = build_match_conditions(middle_name.upper(), 'GIVEN_NAME_2','FULL_NAME') if middle_name else "0"
                                    sur_name_condition = build_match_conditions(sur_name.upper(), 'SURNAME','FULL_NAME') if sur_name else "0"

                                    # addressElement1_condition = build_match_conditions(addressElement1.upper(), 'AD1','FULL_ADDRESS') if addressElement1 else "0"
                                    # addressElement2_condition = build_match_conditions(addressElement2.upper(), 'SUB_DISTRICT','FULL_ADDRESS') if addressElement2 else "0"
                                    # addressElement3_condition = build_match_conditions(addressElement3.upper(), 'REGENCY','FULL_ADDRESS') if addressElement3 else "0"
                                    # addressElement4_condition = build_match_conditions(addressElement4.upper(), 'PROVINCE','FULL_ADDRESS') if addressElement4 else "0"
                                    if id_number:
                                        query = f"""
                                        select * from SHARED_DATA.{table_name} where lower(ID_CARD) = '{id_number.lower()}'
                                        """
                                    else:
                                        query = f"""
                                        WITH matched_records AS (
                                            SELECT
                                                ID_CARD,
                                                FULL_NAME,
                                                GIVEN_NAME_1,
                                                GIVEN_NAME_2,
                                                GIVEN_NAME_3,
                                                SURNAME,
                                                DOB_YYYYMMDD,
                                                DOB_YYYYMMDD_DATE,
                                                FULL_ADDRESS,
                                                AD1,
                                                SUB_DISTRICT,
                                                DISTRICT,
                                                CITY,
                                                REGENCY,
                                                PROVINCE,
                                                POSTCODE,
                                                MOBILE,
                                                EMAIL,
                                                {first_name_condition} AS first_name_score,
                                                {middle_name_condition} AS middle_name_score,
                                                {sur_name_condition} AS sur_name_score,

                
                                                CASE
                                                    WHEN '{dob}' != '' AND DOB_YYYYMMDD_DATE = '{dob}'::DATE
                                                    THEN 1.0
                                                    ELSE 0
                                                END AS dob_score
                                            FROM
                                                SHARED_DATA.{table_name}
                                                -- SHARED_DATA.INDONISIA_SAMPLE_RECORDS
                                                -- code_schema.kyc_view
                                        )
                                        SELECT *
                                        FROM matched_records
                                        WHERE (first_name_score + middle_name_score + sur_name_score + dob_score) >=2
                                        --   + addressElement1_score + addressElement2_score + addressElement3_score \
                                        --   + addressElement4_score) >= 2
                                        ORDER BY (first_name_score + middle_name_score + sur_name_score + dob_score) DESC
                                        --   + addressElement1_score + addressElement2_score + addressElement3_score \
                                        --   + addressElement4_score) DESC
                                        LIMIT 100;
                                        """

                                                                                       

                                    
# ------------------------------------------------------------------ ------------------------------------------------

                    

                                    # Final query
                                    # query = f"""
                                    # SELECT * 
                                    # FROM INDONISIA_RECORDS
                                    # WHERE {where_clause};
                                    # """

                                    # df = pd.read_sql_query(query, conn)
                                    # df = session.sql(query).to_pandas()
                                    # batches = session.sql(query).collect()
                                    # df = pd.DataFrame(batches)

                                    # batches = session.sql(query).to_pandas_batches()
                                    df = session.sql(query).to_pandas()


                                    # df = pd.concat(batches, ignore_index=True)
                                    # result = session.sql(query).collect()
                                    # df = pd.DataFrame([row.as_dict() for row in result])
                                    # st.write('retrieved df',df.head(3))
                                    # st.write(query)
                                    end_time = time.time()

                                    # st.text(f"Time taken to SQL verify: {int((end_time - start_time) * 1000)} ms")

                 

                                    # df = resident_data[resident_data.apply(lambda row: fuzzy_match(row), axis=1)]
                                    # st.write(df.head())
                                    full_name_input = first_name.lower() + " " + middle_name.lower() + " " + sur_name.lower()
                                    # st.write(first_name+" "+middle_name+" "+sur_name)
                                    threshold = 30  # Set the threshold for fuzzy match (0-100)

                                    # Define a function to compute fuzzy match ratio and filter rows
                                    def fuzzy_match(row):
                                        full_name = row['FULL_NAME'].lower() if row['FULL_NAME'] else ""
                                        return fuzz.ratio(full_name, full_name_input) >= threshold


                                    # resident_data = cursor.fetch_pandas_all()
                                    # resident_data = pd.read_sql_query(table_query, conn)
                                    available_columns = df.columns.tolist()
                                    # st.write(available_columns)

                                    requested_columns = ["GIVEN_NAME_1", "GIVEN_NAME_2","GIVEN_NAME_3","SURNAME","DOB_YYYYMMDD","DOB_YYYYMMDD_DATE","FULL_ADDRESS","AD1","SUB_DISTRICT","DISTRICT","CITY","REGENCY","PROVINCE","POSTCODE","MOBILE","EMAIL"]
                                    
                                    if requested_columns:
                                        valid_columns = [col for col in requested_columns if col in available_columns]
                                    else:
                                        valid_columns = available_columns  
                                    
                                    # st.write(valid_columns)
                                    df = df[df.apply(fuzzy_match, axis=1)]
                                    # st.write("filtered_df",df)
                                    # threshold = 30
                                    # def fuzzy_match(row):
                                    #     score = {
                                    #         "FIRSTNAME": fuzz.ratio(row["FIRSTNAME"], first_name),
                                    #         "MIDDLENAME": fuzz.ratio(row["MIDDLENAME"], middle_name),
                                    #         "LASTNAME": fuzz.ratio(row["LASTNAME"], sur_name)
                                    #         # "DOB": fuzz.ratio(row["DOB"], input_data["DOB"])
                                    #     }
                                    #     return all(value >= threshold for value in score.values())
                                    # df = resident_data[resident_data.apply(lambda row: fuzzy_match(row), axis=1)][valid_columns]
                                    # st.write(df)
                    #                 # select_clause = ", ".join(valid_columns)
                    #                 ## query = f"select * from {table_name}"
                    #             #     query = f"""
                    #             #         WITH InputData AS (
                    #             #             SELECT
                    #             #                 '{first_name}' AS first_name_input,
                    #             #                 '{middle_name}' AS middle_name_input,
                    #             #                 '{sur_name}' AS sur_name_input,
                    #             #                 '{dob}' AS dob_input
                    #             #         )
                    #             #     SELECT
                    #             #         {select_clause}
                    #             #     FROM
                    #             #         resident_df AS resident,
                    #             #         InputData AS input
                    #             #     WHERE
                    #             #         (
                    #             #             -- Exact case-insensitive matches, but only if the input is not empty or NULL
                    #             #             (LOWER(input.sur_name_input) IS NOT NULL AND LOWER(input.sur_name_input) != '' AND LOWER(resident.LASTNAME) like LOWER(input.sur_name_input))\
                    #             #             OR (LOWER(input.middle_name_input) IS NOT NULL AND LOWER(input.middle_name_input) != '' AND LOWER(resident.MIDDLENAME) like  LOWER(input.middle_name_input))\
                    #             #             AND (LOWER(input.first_name_input) IS NOT NULL AND LOWER(input.first_name_input) != '' AND LOWER(resident.FIRSTNAME) like  LOWER(input.first_name_input))\
                    #             #             -- OR (input.dob_input IS NOT NULL AND input.dob_input != '' AND resident.DOB = input.dob_input)                         
                    #             #         )

                    #             #     LIMIT 10
                    #             # """
                    #                 # df = pd.read_sql_query(query, conn)
                    #                 # try:
                    #                 # df = pd.read_sql_query(query, conn)
                    #                 # st.write(df.T)
                    #                 # st.write(str(int(df['MOBILE'][0]))==mobile)
                                    if df.empty:
                                        if not multi_sources:
                                            st.warning("No Matching Records: ❌")
                                            break
                                    else:
                                        # df = df.rename(columns={'First_Name':'FIRST_NAME','Gn_1_2':'MIDDLE_NAME','Sur_Name':'SUR_NAME',\
                                        #                         'DOB_Formatted':'DOB','Ad1':'AD1','Suburb':"SUBURB",'State':'STATE',\
                                        #                         'Postcode':'POSTCODE', 'EmailAddress':'EMAILADDRESS'})
                                        # def normalize_name(name):
                                        #     if pd.isna(name):  # Handle None or NaN values
                                        #         return ""
                                        #     return " ".join(sorted(name.split()))
                                        # df['NORMALIZED_NAME'] = df['FULL_NAME'].apply(normalize_name)
                                        # normalized_input = normalize_name(full_name_input)
                                        # st.write(normalized_input)
                                        # df['SIMILARITY'] = df['NORMALIZED_NAME'].apply(
                                        #         lambda x: fuzz.partial_ratio(x, normalized_input) * 100
                                        #     )
                                        df['match_score'] = (
                                            df['FULL_NAME'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", first_name.lower()), textdistance.jaro_winkler(x.lower() if x else "", first_name.lower()) * 100)) +\
                                            df['FULL_NAME'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", middle_name.lower()), textdistance.jaro_winkler(x.lower() if x else "", middle_name.lower()) * 100)) +\
                                            df['FULL_NAME'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", sur_name.lower()), textdistance.jaro_winkler(x.lower() if x else "", sur_name.lower()) * 100)) +\
                                            df['DOB_YYYYMMDD_DATE'].apply(lambda x: 75 if x and dob and x == date.fromisoformat(dob) else 0) +\
                                            df['FULL_ADDRESS'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", addressElement1.lower()), textdistance.jaro_winkler(x.lower() if x else "", addressElement1.lower()) * 100)) +\
                                            df['FULL_ADDRESS'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", addressElement2.lower()), textdistance.jaro_winkler(x.lower() if x else "", addressElement2.lower()) * 100)) +\
                                            df['FULL_ADDRESS'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", addressElement3.lower()), textdistance.jaro_winkler(x.lower() if x else "", addressElement3.lower()) * 100)) +\
                                            df['FULL_ADDRESS'].apply(lambda x: max(fuzz.token_set_ratio(x.lower() if x else "", addressElement4.lower()), textdistance.jaro_winkler(x.lower() if x else "", addressElement4.lower()) * 100))
                                        )
                                        # # # Add DOB match score if the "DOB" column exists
                                        # if "DOB" in df.columns:
                                        #     df['match_score'] += (
                                        #         ((df['DOB_YYYYMMDD'].notna()) & (df['DOB_YYYYMMDD'] == dob.lower())).astype(int) * 3  # Match DOB (weight 3) if not null
                                        #     )


                                        # # # # Step 3: Sort the DataFrame by match_score in descending order
                                        df_sorted = df.sort_values(by='match_score', ascending=False).reset_index(drop=True)
                                        # st.write("df_sorted",df_sorted.head(3))
                                        # st.write("df_sorted",df.sort_values(by='match_score', ascending=False))
                                        df = df.sort_values(by='match_score', ascending=False).head(1)
                                        end_time = time.time()

                                        st.text(f"Time taken to verify: {int((end_time - start_time) * 1000)} ms")

                                        st.markdown(':orange[**Matched Output Records:**]')

                                        # Convert DOB column to date format if it exists
                                        df['DOB'] = pd.to_datetime(df['DOB_YYYYMMDD'].str.split('.').str[0], format='%Y%m%d').dt.date
                                        # st.write("df",df)
                                        # st.write("df final",df)
                                        # st.write("GIVEN_NAME_2",df['GIVEN_NAME_2'])
                                        # st.write("middle_name",middle_name)
                                        # st.write(df['GIVEN_NAME_2'].apply(lambda x: fuzz.token_sort_ratio(x.lower(), middle_name.lower())))
                                        df = df.fillna("").replace('<NA>','').reset_index(drop=True)
                                        fields = [
                                        ('GIVEN_NAME_1', first_name, 0),
                                        ('GIVEN_NAME_2', middle_name.split()[0] if middle_name else "", 1),
                                        ('SURNAME', sur_name, 3)
                                            ]
                                        if middle_name and len(middle_name.split()) > 1:
                                            fields.append(('GIVEN_NAME_3', middle_name.split()[1], 2))
                                        def update_name_str(row):
                                            name_Str = "XXXX" 
                                            for db_column, input_field, str_index in fields:
                                                name_Str = apply_name_matching(row, name_Str, db_column, input_field, str_index)
                                            return name_Str
                                        df['Name Match Str'] = df.apply(update_name_str, axis=1)
                                        df['First Name Similarity'] = df.apply(lambda row: max(textdistance.jaro_winkler(row['FULL_NAME'].lower(), first_name.lower()) * 100, fuzz.partial_token_sort_ratio(row['FULL_NAME'].lower(), first_name.lower())), axis=1).astype(int)
                                        df['Middle Name Similarity'] = df.apply(lambda row: max(textdistance.jaro_winkler(row['FULL_NAME'].lower(), middle_name.lower()) * 100, fuzz.partial_token_sort_ratio(row['FULL_NAME'].lower(), middle_name.lower())), axis=1).astype(int)
                                        # df['Given Name 3 Similarity'] = df.apply(lambda row: max(textdistance.jaro_winkler(row['FULL_NAME'].lower(), middle_name.lower()) * 100, fuzz.partial_token_sort_ratio(row['FULL_NAME'].lower(), middle_name.lower())), axis=1).astype(int)
                                        df['Surname Similarity'] = df.apply(lambda row: max(textdistance.jaro_winkler(row['FULL_NAME'].lower(), sur_name.lower()) * 100, fuzz.partial_token_sort_ratio(row['FULL_NAME'].lower(), sur_name.lower())), axis=1).astype(int)

                                        # df['Given Name 1 Similarity'] = int(df['FULL_NAME'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), first_name.lower()) ).apply(lambda score: int(score) if score > 65 else 0)) 
                                        # df['Given Name 2 Similarity'] = int(df['FULL_NAME'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), middle_name.lower())).apply(lambda score: int(score) if score > 65 else 0))
                                        # df['Given Name 3 Similarity'] = int(df['FULL_NAME'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), middle_name.lower())).apply(lambda score: int(score) if score > 65 else 0))
                                        
                                        # df['Given Name 2 Similarity'] = int(df['GIVEN_NAME_2'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), middle_name.lower())))
                                        # df['SurName Similarity'] = int(df['FULL_NAME'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), sur_name.lower())).apply(lambda score: score if int(score) > 65 else 0) )
                                        if df['Name Match Str'][0][0] == 'T':
                                            df['Given Name 1 Similarity'] = 100
                                        if df['Name Match Str'][0][1] == 'T':
                                            df['Given Name 2 Similarity'] = 100
                                        if df['Name Match Str'][0][2] == 'T':
                                            df['SurName Similarity'] = 100
                                        # st.write("df final",df)

                                        full_name_request = (first_name.strip() + " " + middle_name.strip() + " "+ sur_name.strip()).strip().lower()
                                        # full_name_matched = (df['GIVEN_NAME_1'][0].strip()+ " "+df['GIVEN_NAME_2'][0].strip()+ " "+df['SURNAME'][0].strip()).lower()
                                        full_name_matched = (df['FULL_NAME'][0].strip()).lower()
                                        name_obj = Name(full_name_request)
                                        match_results = {
                                            "Exact Match": ((df['Name Match Str'] == 'EEEE') |(df['Name Match Str'] == 'EEXE') ).any(),
                                            "Hyphenated Match": name_obj.hyphenated(full_name_matched),
                                            "Transposed Match": name_obj.transposed(full_name_matched),
                                            # "Middle Name Mismatch": df['Name Match Str'].str.contains('E.*E$', regex=True).any(),
                                            "Middle Name Mismatch": df['Name Match Str'][0].startswith('E') and df['Name Match Str'][0].endswith('E'),
                                            "Initial Match": name_obj.initial(full_name_matched),
                                            "SurName only Match": df['Name Match Str'].str.contains('^[ETMD].*E$', regex=True).any(),
                                            # "Middle Name Mismatch": df['Name Match Str'].str.contains('E.*E$', regex=True).any(),
                                            "Fuzzy Match": name_obj.fuzzy(full_name_matched),
                                            "Nickname Match": name_obj.nickname(full_name_matched),
                                            "Missing Part Match": name_obj.missing(full_name_matched),
                                            "Different Name": name_obj.different(full_name_matched)
                                        }
                                        # Filter out any matches that returned False
                                        match_results = {k: v for k, v in match_results.items() if v}
                                        top_match = next(iter(match_results.items()), ("No Match Found", ""))

                                        df['Name Match Level'] = top_match[0]
                                        # st.write(df.T)
                                        # st.write("full_name_request",full_name_request)
                                        # st.write("full_name_matched",full_name_matched)

                                        # st.write(fuzz.token_sort_ratio(full_name_request.lower(),full_name_matched.lower()))

                                        # df['full_name_similarity'] = (fuzz.token_set_ratio(full_name_request,full_name_matched)) 
                                        # df['full_name_similarity'] = df.apply(lambda row: textdistance.jaro_winkler(row['FULL_NAME'].lower(), full_name_input.lower()) * 100, axis=1)
                                        df['full_name_similarity'] = df.apply(lambda row: max(textdistance.jaro_winkler(row['FULL_NAME'].lower(), full_name_input.lower()) * 100, fuzz.partial_token_sort_ratio(row['FULL_NAME'].lower(), full_name_input.lower())), axis=1)
                                        df['full_name_similarity'] = df['full_name_similarity'].apply(lambda score: int(score) if score > 65 else 0)
                                        if fuzz.token_sort_ratio(full_name_request,full_name_matched)==100 and top_match[0] !='Exact Match':
                                            # st.write('test')
                                            df['full_name_similarity'] = 100
                                            # df['Match Level'] = 'Transposed Match'
                                        if 'DOB' in df.columns:
                                            df['dob_match'] = True if df['DOB'].apply(lambda x: Dob(dob).exact(x))[0]=='Exact Match' else False
                                        # st.write("df final",df)
                                        

                                        
                                        if country_prefix in ('indonisia','mx'):
                                            df['addressElement1_similarity'] = df[['FULL_ADDRESS', 'AD1']].apply(lambda x: max(fuzz.token_set_ratio(x[0].lower(), addressElement1.lower()), fuzz.partial_token_sort_ratio(x[1].lower(), addressElement1.lower())), axis=1).apply(lambda score: int(score) if score > 65 else 0) 
                                            weight1 = 50 if 85<=df['addressElement1_similarity'][0] <=100 else 30 if 70<=df['addressElement1_similarity'][0] <85 else 0 
                                            
                                            df['addressElement2_similarity'] = df[['FULL_ADDRESS', 'SUB_DISTRICT']].apply(lambda x: max(fuzz.token_set_ratio(x[0].lower(), addressElement2.lower()), fuzz.partial_token_sort_ratio(x[1].lower(), addressElement2.lower())), axis=1).apply(lambda score: int(score) if score > 65 else 0) 
                                            weight2 = 20 if 85<=df['addressElement2_similarity'][0] <=100 else 25 if 70<=df['addressElement2_similarity'][0] <85 else 0 
                                            
                                            df['addressElement3_similarity'] = df[['FULL_ADDRESS', 'REGENCY']].apply(lambda x: max(fuzz.token_set_ratio(x[0].lower(), addressElement3.lower()), fuzz.partial_token_sort_ratio(x[1].lower(), addressElement3.lower())), axis=1).apply(lambda score: int(score) if score > 65 else 0)  
                                            weight3 = 10 if 85<=df['addressElement3_similarity'][0] <=100 else  0

                                            # df['addressElement4_similarity'] = df['PROVINCE'].apply(lambda x: fuzz.partial_token_sort_ratio(x.lower(), addressElement4.lower())).apply(lambda score: int(score) if score > 65 else 0) 
                                            df['addressElement4_similarity'] = df[['FULL_ADDRESS', 'PROVINCE']].apply(lambda x: max(fuzz.token_set_ratio(x[0].lower(), addressElement4.lower()), fuzz.partial_token_sort_ratio(x[1].lower(), addressElement4.lower())), axis=1).apply(lambda score: int(score) if score > 65 else 0)
                                            weight4 = 20 if 85<=df['addressElement4_similarity'][0] <=100 else 0 
                                            

                                            total_weight = weight1+weight2+weight3+weight4

                                        else:
                                            total_weight = textdistance.jaro_winkler(df['ADDRESS'][0].lower().strip(), address_line1.lower().strip()) * 100
                                            df['address_line_similarity'] = total_weight



                                        if total_weight > 90:
                                            # match_level = f'Full Match, {total_weight}'
                                            match_level = "Full Match"
                                            Full_Address_Score = total_weight

                                        elif 70 <= total_weight <= 90:
                                            # match_level = f'Partial Match, {total_weight}'
                                            match_level = 'Partial Match'
                                            Full_Address_Score = total_weight
                                        

                                        else:
                                            match_level = 'No Match'
                                            Full_Address_Score = total_weight

                                        df['Address Match Level'] = match_level
                                        df['Full Address Score'] = Full_Address_Score
                                        # st.write('df final2',df)
                                        matching_levels = get_matching_level(df,dob,mobile,email,df['full_name_similarity'][0],total_weight)
                                        df['Overall Matching Level'] = ', '.join(matching_levels)
                                        # st.write('test',(df['MOBILE'].iloc[0])=="")
                                        matching_levels1 = get_mobile_email_matching_level(df,dob,mobile,email,df['full_name_similarity'][0],total_weight)
                                        df['Overall Matching Level1'] = ', '.join(matching_levels1)

                                        df["Overall Verified Level"] = append_based_on_verification(df,verified_by=True)
                                        df["Overall Contact Verified Level"] = append_mobile_email_verification(df,verified_by=True)
                                        # st.write(df.T)


                                        # level_verifications = []
                                        # for  name in ["FullName", "PartialName"]:
                                        #     if name in df['Overall Matching Level'][0]:
                                        #         df['FullName'] = True
                                        #         level_verifications.append('FullName')
                                        # for  address in ["FullAddress", "PartialAddress"]:
                                        #     if address in df['Overall Matching Level'][0]:
                                        #         df['FullAddress'] = True
                                        #         level_verifications.append('FullAddress')
                                        # if 'DOB' in df['Overall Matching Level'][0]:
                                        #     df['DateOfBirth'] = True
                                        #     level_verifications.append('DateOfBirth')
                                        # if "Mobile" in df['Overall Matching Level'][0]:
                                        #     df['Mobile'] = True
                                        #     level_verifications.append('Mobile')
                                        # if "Email" in df['Overall Matching Level'][0]:
                                        #     df['Email'] = True
                                        #     level_verifications.append('Email')

                                        if (df['Overall Verified Level'][0]  != 'No Match' ):
                                            df['IDV Record Verified'] = True
                                            # df['MultiSource'] = False
                                            df['IDV Multi Level Verification'] = False
                                            multi_sources_score.append(df['Overall Verified Level'][0])
                                            multi_mobile_email_score.append(df['Overall Contact Verified Level'][0])

                                            # st.write(df)

                                        else:
                                            
                                            # st.warning("No Matching Records: ❌")

                                            df['IDV Record Verified'] = False
                                            # df['MultiSource'] = False
                                            df['IDV Multi Level Verification'] = False



                                        df_transposed = df.T
                                        df_transposed.columns = ['Results']
                                        dfs[table_name] = df_transposed
                                        # st.write(df_transposed)
                                        # index_col = ['Name Match Str','Name Match Level','dob_match','Address Matching String',
                                        #                                 'Address Match Level','Overall Matching Level','Overall Verified Level']
                                        # index_col = level_verifications + ['Overall Matching Level','Overall Matching Level1']
                                        index_col = ['Overall Verified Level','Overall Contact Verified Level','IDV Record Verified','IDV Multi Level Verification']
                                        with st.expander("🔖 :red[**Summary Data:**]",expanded=True):
                                            if df_transposed.loc['Overall Matching Level']['Results'] == '' or df_transposed.loc['Overall Verified Level']['Results'] == 'No Match':
                                                # st.warning("No Matching Records: ❌")
                                                multi_sources_score.append('No Match')
                                                multi_mobile_email_score.append('No Match')
                                                if not multi_sources:
                                                    st.warning("No Matching Records: ❌")
                                                    break
                                            # st.write(df_transposed.loc['Overall Matching Level'])
                                            else:
                                                # st.dataframe(df_transposed.loc[index_col].rename({"Overall Verified Level":"IDV Verified Level","Overall Contact Verified Level":"IDV Contact Verified Level"}), width=550, height=200)
                                                df_transposed_new = df_transposed.loc[index_col].rename({"Overall Verified Level":"IDV Verified Level","Overall Contact Verified Level":"IDV Contact Verified Level"})
                                                IDV_Verified_Level = {
                                                    "M1": "Full Name Full Address DOB Match",
                                                    "N1": "Full Name Full Address Match",
                                                    "M2": "Full Name DOB Match",
                                                    "P1": "Full Name, Mobile, and Email",
                                                    "P2": "Full Name and Mobile",
                                                    "P3": "Full Name and Email"}
                                                
                                                MultiSourceLevel = {
                                                    True: "Verified by two or more independent sources",
                                                    False: "Failed MultiSources verification"
                                                }
                                                SingleSourceLevel = {
                                                    True: "A Verified Record with multiple attributes",
                                                    False: "Non Verified Record"}
                                                ID_Level ={
                                                    True: "ID Number Verified",
                                                    False: "ID Number Not Verified"
                                                }
                                                
                                                scoring_description = {
                                                    "Exact Match": "All name components match in the same order.",
                                                    "Initial Match": "First name component match their initials.",
                                                    "Middle Name Mismatch": "The middle name differs or is missing.",
                                                    "Hyphenated Match": "Matches names with hyphenated variations.",
                                                    "SurName Only Match": "Only the surname matches, other components differ.",
                                                    "Transposed Match": "Name components are swapped.",
                                                    "Full Match": "Full address match.",
                                                    "Partial Match": "Partial address match.",
                                                    "No Match": "No matching records found.",
                                                    True: "Exactly matched with  DOB",
                                                    False: "No DOB Match",
                                                }
                                                
                                                # df_transposed_new.loc[['IDV Record Verified','Description']] = df_transposed_new['Results'].apply(lambda x: MultiSourceLevel.get(x, ''))

                                                # Add tooltips for IDV Verified Level and IDV Contact Verified Level based on the mapping
                                                # df_transposed_new['Description'] = df_transposed_new['Results'].apply(lambda x: MultiSourceLevel.get(x, ''))
                                                df_transposed_new['Description'] = df_transposed_new['Results'].apply(lambda x: IDV_Verified_Level.get(x, ''))
                                                # df_transposed_new['Description'] = df_transposed_new['Results'].apply(
                                                #         lambda x: ', '.join([description for key, description in IDV_Verified_Level.items() if key in str(x)])
                                                #     )
                                                # df_transposed_new['MultiSource_Description'] = df_transposed_new['Results'].apply(lambda x: MultiSourceLevel.get(x, ''))
                                                # df_transposed_new.loc['MultiSource', 'Description'] = df_transposed_new.loc['MultiSource', 'Results'].apply(lambda x: MultiSourceLevel.get(x, ''))
                                                df_transposed_new.loc['IDV Record Verified', 'Description'] = SingleSourceLevel.get(df_transposed_new.loc['IDV Record Verified', 'Results'], '')
                                                df_transposed_new.loc['IDV Multi Level Verification', 'Description'] = MultiSourceLevel.get(df_transposed_new.loc['IDV Multi Level Verification', 'Results'], '')
                                                
                                                if not multi_sources and df['IDV Record Verified'][0] == True:
                                                    if json_view:
                                                        json_data = df_transposed_new["Results"].to_dict()
                                                        st.json(json_data)
                                                        single_json['Summary'] = json_data
                                                        if table_name not in single_json['Sources']:
                                                            single_json['Sources'][table_name] = {
                                                                "Profile": [],
                                                                "Scoring": []
                                                            }
                                                    else:
                                                        if id_number:
                                                            if df_transposed.loc['ID_CARD','Results']==id_number:
                                                                df_transposed_new.loc['NIK Verified', 'Results'] = True
                                                            else :
                                                                df_transposed_new.loc['NIK Verified', 'Results'] = False
                                                            # st.write("df_transposed.loc['ID_CARD']", df_transposed.loc['ID_CARD','Results'])
                                                            df_transposed_new.loc['NIK Verified', 'Description'] = ID_Level.get(df_transposed_new.loc['NIK Verified', 'Results'], '')

                                                            st.dataframe(df_transposed_new.reindex(
                                                                index=['NIK Verified','IDV Record Verified','IDV Verified Level', 'IDV Contact Verified Level',  'IDV Multi Level Verification']), width=550, height=200)
                                                        else:
                                                            st.dataframe(df_transposed_new.reindex(
                                                                index=['IDV Record Verified','IDV Verified Level', 'IDV Contact Verified Level',  'IDV Multi Level Verification']), width=550, height=200)
                                    # st.write(df_transposed)
                                    if not multi_sources and not df.empty and df['IDV Record Verified'][0] == True:
                                        with st.expander(":red[**Detailed Data:**]", expanded=True):
                                            st.markdown(':green[**Profile**]')
                                            if country_prefix in ('au','nz'):
                                                df_transposed.loc['POSTCODE', 'Results'] = str(int(df_transposed.loc['POSTCODE', 'Results']))
                                            # st.write(df_transposed)
                                            # if df_transposed['Mobile'][0] != "":
                                            # df_transposed.loc['MOBILE', 'Results'] = (int(df_transposed.loc['MOBILE', 'Results']))
                                            if country_prefix in ('au','nz'):
                                                system_returned_df = df_transposed.loc[["FIRSTNAME","MIDDLENAME","LASTNAME","DOB","ADDRESS","SUBURB",
                                                                                    "STATE","POSTCODE","MOBILE","EMAIL"]]
                                            else:
                                                # system_returned_df = df_transposed.loc[["FIRSTNAME","MIDDLENAME","LASTNAME","DOB","ADDRESS","MOBILE","EMAIL"]]    
                                                system_returned_df = df_transposed.loc[valid_columns]
                                                system_returned_df.loc['MiddleName'] = system_returned_df.loc['GIVEN_NAME_2'].fillna('') + ' ' + system_returned_df.loc['GIVEN_NAME_3'].fillna('') 

                                                if 'DOB_YYYYMMDD' in system_returned_df.index and 'GIVEN_NAME_2' in system_returned_df.index and 'GIVEN_NAME_3' in system_returned_df.index:
                                                    system_returned_df = system_returned_df.drop(['DOB_YYYYMMDD','GIVEN_NAME_2','GIVEN_NAME_3'])
                                                if 'DOB' not in system_returned_df.index:
                                                    system_returned_df.loc['DOB'] = df_transposed.loc['DOB']

                                            # system_returned_df.rename({"FIRST_NAME":"First Name","MIDDLE_NAME":"Middle Name", "SUR_NAME":"Last Name","AD1":"Ad1",
                                            #                         "SUBURB":"Suburb","STATE":"State","POSTCODE":"Postcode","Phone2_Mobile":"Mobile",
                                            #                         "EMAILADDRESS":"EmailAddress"},inplace=True)
                                            if json_view:
                                                system_returned_df_json_data = system_returned_df["Results"].to_dict()
                                                st.json(system_returned_df_json_data)
                                                single_json['Sources'][table_name]['Profile'] = system_returned_df_json_data

                                            else:
                                                
                                                # ------------------------------------ Test ------------------------------------

                                                system_returned_df_test = system_returned_df.copy()
                                                # system_returned_df_test = system_returned_df_test.loc[['GIVEN_NAME_1', 'GIVEN_NAME_2', 'GIVEN_NAME_3','GIVEN_NAME_4','GIVEN_NAME_5','GIVEN_NAME_6','GIVEN_NAME_7', 'SURNAME']]
                                                # system_returned_df_test = system_returned_df_test.loc[['GIVEN_NAME_1', 'GIVEN_NAME_2', 'GIVEN_NAME_3', 'SURNAME']]
                                                # system_returned_df_test.loc['MiddleName'] = system_returned_df_test.loc['GIVEN_NAME_2'].fillna('') + ' ' + system_returned_df_test.loc['GIVEN_NAME_3'].fillna('') 
                                                    # ' ' + system_returned_df_test.loc['GIVEN_NAME_4'].fillna('') +
                                                    # '' + system_returned_df_test.loc['GIVEN_NAME_5'].fillna('') + ' ' + system_returned_df_test.loc['GIVEN_NAME_6'].fillna('') + ' ' + system_returned_df_test.loc['GIVEN_NAME_7'].fillna('')
                                                system_returned_df.rename(index={
                                                    'GIVEN_NAME_1': 'FirstName',
                                                    # 'GIVEN_NAME_2': 'MiddleName',
                                                    # 'GIVEN_NAME_3': 'MiddleName',
                                                    'SURNAME': 'Surname'
                                                }, inplace=True)
                                                index_order = ['FirstName', 'MiddleName', 'Surname',"FULL_ADDRESS", 'AD1', 'SUB_DISTRICT', "DISTRICT","CITY","REGENCY","PROVINCE","POSTCODE","MOBILE","EMAIL", 'DOB']
                                                # st.write('test',system_returned_df_test.loc[['FirstName', 'MiddleName', 'SurName']])

                                                # Show the masked or unmasked DataFrame based on the checkbox state
                                                if st.session_state.show_dataframe:
                                                    if st.session_state.remove_anonymization:
                                                        system_returned_df = system_returned_df  # Show the original DataFrame without masking
                                                    else:
                                                        system_returned_df = system_returned_df.applymap(mask_value)  # Show the masked DataFrame
                                                # ------------------------------------------------------------------------------
                                                # st.dataframe(system_returned_df.reindex(index=index_order), width=550, height=400) 
                                            # Mask the values in the DataFrame
 
                                                    st.dataframe(system_returned_df.reindex(index=index_order), width=550, height=400) 

                                            
                                            if country_prefix in ('au','nz'):
                                                similarity_returned_df = df_transposed.loc[["Given Name 1 Similarity","Given Name 2 Similarity","SurName Similarity",
                                                    "full_name_similarity","Name Match Level","dob_match","address_line_similarity",
                                                    "suburb_similarity","state_similarity","postcde_similarity","Address Match Level","Full Address Score"]]
                                                col_order = ["Name Match Level", "Full Name Score", "Given Name 1 Score", "Given Name 2 Score",
                                                        "SurName Score", "Address Match Level", "Full Address Score","Address Line Score",
                                                        "Suburb Score","State Score", "Postcde Score","DOB Match"]
                                            if country_prefix in ('indonisia','mx'):
                                                similarity_returned_df = df_transposed.loc[["First Name Similarity","Middle Name Similarity","Surname Similarity",
                                                    "full_name_similarity","Name Match Level","dob_match","addressElement1_similarity",
                                                    "addressElement2_similarity","addressElement3_similarity","addressElement4_similarity","Address Match Level","Full Address Score"]]
                                                col_order = ["Name Match Level", "Full Name Score", "First Name Score", "Middle Name Score",
                                                        "Surname Score", "Address Match Level", "Full Address Score","AddressElement1 Score",
                                                        "AddressElement2 Score","AddressElement3 Score","AddressElement4 Score","DOB Match"]
                                            else:
                                                similarity_returned_df = df_transposed.loc[["Given Name 1 Similarity","Given Name 2 Similarity","SurName Similarity",
                                                    "full_name_similarity","Name Match Level","dob_match","address_line_similarity",
                                                    "Address Match Level","Full Address Score"]]
                                                col_order = ["Name Match Level", "Full Name Score", "Given Name 1 Score", "Given Name 2 Score",
                                                        "SurName Score", "Address Match Level", "Full Address Score","Address Line Score",
                                                        "DOB Match"]

                                            st.markdown(':green[**Scoring**]')
                                            if country_prefix not in ('indonisia','mx'):
                                                similarity_returned_df.rename({"Given Name 1 Similarity":"Given Name 1 Score", "Given Name 2 Similarity":"Given Name 2 Score",
                                                                        "SurName Similarity":"SurName Score","full_name_similarity":"Full Name Score",
                                                                        "dob_match":"DOB Match","address_line_similarity":"Address Line Score","suburb_similarity":"Suburb Score",
                                                                        "state_similarity":"State Score","postcde_similarity":"Postcde Score"},inplace=True)
                                            
                                            else:
                                                similarity_returned_df.rename({"First Name Similarity":"First Name Score", "Middle Name Similarity":"Middle Name Score",
                                                                        "Surname Similarity":"Surname Score","full_name_similarity":"Full Name Score",
                                                                        "dob_match":"DOB Match","addressElement1_similarity":"AddressElement1 Score",
                                                                        "addressElement2_similarity":"AddressElement2 Score","addressElement3_similarity":"AddressElement3 Score",
                                                                        "addressElement4_similarity":"AddressElement4 Score"},inplace=True)

                                            if json_view:
                                                similarity_returned_df_json_data = similarity_returned_df["Results"].to_dict()
                                                st.json(similarity_returned_df_json_data)
                                                single_json['Sources'][table_name]['Scoring'] = similarity_returned_df_json_data
                                            else:
                                                similarity_returned_df['Input matching percentage with source'] = similarity_returned_df.apply(
                                                    lambda row: f"{row.name.replace(' Score', '')} {row['Results']}% match with source" 
                                                    if isinstance(row['Results'], (int, float)) and row.name not in ("DOB Match") else scoring_description.get(row['Results'], ''), axis=1)
                                                st.dataframe(similarity_returned_df.reindex(col_order), width=550, height=480)    

                                            break
                            with col11:        
                                display_match_explanation_new() 

                            IDV_Verified_Level = {
                                "M1": "Full Name Full Address DOB Match",
                                "N1": "Full Name Full Address Match",
                                "M2": "Full Name DOB Match",
                                "P1": "Full Name, Mobile, and Email",
                                "P2": "Full Name and Mobile",
                                "P3": "Full Name and Email"}
                            scoring_description = {
                                "Exact Match": "All name components match in the same order.",
                                "Initial Match": "First name component match their initials.",
                                "Middle Name Mismatch": "The middle name differs or is missing.",
                                "Hyphenated Match": "Matches names with hyphenated variations.",
                                "SurName Only Match": "Only the surname matches, other components differ.",
                                "Transposed Match": "Name components are swapped.",
                                "Full Match": "Full address match.",
                                "Partial Match": "Partial address match.",
                                "No Match": "No matching records found.",
                                True: "Exactly matched with DOB",
                                False: "No DOB Match",
                            }

                            MultiSourceLevel = {
                                True: "Verified by two or more independent sources",
                                False: "Failed MultiSources verification"
                            }
                            SingleSourceLevel = {
                                True: "A Verified Record with multiple attributes",
                                False: "Non Verified Record"}
                            # st.write(dfs.values())                        
                            if multi_sources:  
                                d = Counter(multi_sources_score)
                                is_multi_source = (
                                    (d.get("M1", 0) >= 2) or
                                    (d.get("M1", 0) >= 1 and d.get("M2", 0) >= 1) or
                                    (d.get("M1", 0) >= 1 and d.get("N1", 0) >= 1) or
                                    (d.get("M2", 0) >= 1 and d.get("N1", 0) >= 1)
                                )

                                # Prepare DataFrame
                                multi_df = pd.DataFrame(
                                    {
                                        # "IDV Multi Verified Levels": [", ".join(d.keys())],
                                        "IDV Verified Level" : [", ".join(multi_sources_score)],
                                        "IDV Contact Verified Level" : [", ".join(multi_mobile_email_score)],
                                        "IDV Record Verified" : [True if df['IDV Record Verified'][0]==True else False],
                                        # "IDV Multi Verified Levels": [", ".join([f"{key}: {value}" for key, value in d.items()])],
                                        "IDV Multi Level Verification": [True if is_multi_source else False] 
                                    }
                                )
                                multi_df = multi_df.T
                                multi_df.columns = ['Results']
                                multi_df['Description'] = multi_df['Results'].apply(lambda x: IDV_Verified_Level.get(x, ''))
                                # df_transposed_new['MultiSource_Description'] = df_transposed_new['Results'].apply(lambda x: MultiSourceLevel.get(x, ''))
                                # df_transposed_new.loc['MultiSource', 'Description'] = df_transposed_new.loc['MultiSource', 'Results'].apply(lambda x: MultiSourceLevel.get(x, ''))
                                multi_df.loc['IDV Record Verified', 'Description'] = SingleSourceLevel.get(multi_df.loc['IDV Record Verified', 'Results'], '')
                                multi_df.loc['IDV Multi Level Verification', 'Description'] = MultiSourceLevel.get(multi_df.loc['IDV Multi Level Verification', 'Results'], '')

                                with st.expander("🔖 :red[**Summary Data:**]",expanded=True):
                                    if json_view:
                                        json_data = multi_df["Results"].to_dict()
                                        st.json(json_data)  
                                        single_json['Summary'] = json_data
                                        # if table_name not in single_json['Detailed Data']:
                                        #     single_json['Detailed Data'][table_name] = {
                                        #         "Profile": [],
                                        #         "Scoring": []
                                        #     }
                                    else:                  
                                        st.dataframe(multi_df, width=550, height=200)

                                for source,df in dfs.items():
                                    if source not in single_json['Sources']:
                                            single_json['Sources'][source] = {
                                                "Profile": [],
                                                "Scoring": []
                                            }
                                    with st.expander(f":red[**{source} - Detailed Data:**]" ,expanded=True):
                                        st.markdown(':green[**Profile**]')
                                        if 'POSTCODE' in df.index:
                                            df.loc['POSTCODE', 'Results'] = str(int(df.loc['POSTCODE', 'Results']))
                                        if 'MOBILE' in df.index:
                                            df.loc['MOBILE', 'Results'] = str(int(df.loc['MOBILE', 'Results']))
                                        # system_returned_df = df.loc[["FIRSTNAME","MIDDLENAME","LASTNAME","DOB","ADDRESS","SUBURB",
                                        #                                         "STATE","POSTCODE","MOBILE","EMAIL"]]
                                        columns_to_select = ["FIRSTNAME", "MIDDLENAME", "LASTNAME", "DOB", "ADDRESS", 
                                                        "SUBURB", "STATE", "POSTCODE", "MOBILE", "EMAIL"]

                                        existing_columns = df.index.intersection(columns_to_select)
                                        system_returned_df = df.loc[existing_columns]
                                        # system_returned_df.rename({"FIRST_NAME":"First Name","MIDDLE_NAME":"Middle Name", "SUR_NAME":"Last Name","AD1":"Ad1",
                                        #                         "SUBURB":"Suburb","STATE":"State","POSTCODE":"Postcode","Phone2_Mobile":"Mobile",
                                        #                         "EMAILADDRESS":"EmailAddress"},inplace=True)
                                        if json_view:
                                            system_returned_df_json_data = system_returned_df["Results"].to_dict()
                                            st.json(system_returned_df_json_data)
                                            single_json['Sources'][source]['Profile'] = system_returned_df_json_data

                                        else:
                                            st.dataframe(system_returned_df, width=550, height=400) 

                                        similarity_columns_to_select = ["Given Name 1 Similarity","Given Name 2 Similarity","SurName Similarity",
                                                "full_name_similarity","Name Match Level","dob_match","address_line_similarity",
                                                "suburb_similarity","state_similarity","postcde_similarity","Address Match Level","Full Address Score", "Overall Verified Level",
                                                "Overall Contact Verified Level"]

                                        similarity_existing_columns = df.index.intersection(similarity_columns_to_select)
                                        similarity_returned_df = df.loc[similarity_existing_columns]
                                        # similarity_returned_df = df.loc[["Given Name 1 Similarity","Given Name 2 Similarity","SurName Similarity",
                                        #         "full_name_similarity","Name Match Level","dob_match","address_line_similarity",
                                        #         "suburb_similarity","state_similarity","postcde_similarity","Address Match Level","Full Address Score"]]
                        
                                        st.markdown(':green[**Scoring**]')
                                        similarity_returned_df.rename({"Given Name 1 Similarity":"Given Name 1 Score", "Given Name 2 Similarity":"Given Name 2 Score",
                                                                    "Surname Similarity":"SurName Score","full_name_similarity":"Full Name Score",
                                                                    "dob_match":"DOB Match","address_line_similarity":"Address Line Score","suburb_similarity":"Suburb Score",
                                                                    "state_similarity":"State Score","postcde_similarity":"Postcde Score"},inplace=True)
                                        
                                        col_order = ["Name Match Level", "Full Name Score", "Given Name 1 Score", "Given Name 2 Score",
                                                    "SurName Score", "Address Match Level", "Full Address Score","Address Line Score",
                                                    "Suburb Score","State Score", "Postcde Score","DOB Match","Overall Verified Level","Overall Contact Verified Level"]
                                        # similarity_returned_df['Description'] = similarity_returned_df['Results'].apply(lambda x: scoring_description.get(x, ''))

                                        if json_view:
                                            similarity_returned_df_json_data = similarity_returned_df["Results"].to_dict()
                                            st.json(similarity_returned_df_json_data)
                                            single_json['Sources'][source]['Scoring'] = similarity_returned_df_json_data

                                        else:
                                            st.dataframe(similarity_returned_df.reindex(col_order), width=1000, height=500) 
                                            # pass 
                            with col11:
                                if json_view:        
                                    st.json(single_json)
                                    