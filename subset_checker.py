import pandas as pd

# Load the data
Sample_Profiles = pd.read_csv("artifacts/2025-05-31T13-26_export.csv").set_index('Unnamed: 0').fillna("ALL")

print("Sample Profiles DataFrame:")
print(Sample_Profiles)
print("\n" + "="*50 + "\n")

def is_subset_group(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b
    
    Args:
        group_a_name: Name of the potential subset group
        group_b_name: Name of the potential parent group  
        profiles_df: DataFrame with group profiles
    
    Returns:
        bool: True if group_a is a subset of group_b
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False
    
    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]
    
    print(f"Checking if {group_a_name} is a subset of {group_b_name}:")
    print(f"{group_a_name} values: {group_a.to_dict()}")
    print(f"{group_b_name} values: {group_b.to_dict()}")
    
    # Check each attribute/index
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()
        
        print(f"\nAttribute '{attribute}':")
        print(f"  {group_a_name}: '{val_a}'")
        print(f"  {group_b_name}: '{val_b}'")
        
        # If group_b has "ALL", it contains everything, so group_a is subset
        if val_b == "ALL":
            print(f"  → {group_b_name} has 'ALL' for {attribute}, so {group_a_name} is subset")
            continue
            
        # If group_a has "ALL" but group_b doesn't, then group_a is NOT a subset
        if val_a == "ALL" and val_b != "ALL":
            print(f"  → {group_a_name} has 'ALL' but {group_b_name} doesn't, so NOT a subset")
            return False
            
        # If both have specific values, check if group_a values are contained in group_b
        if val_a != "ALL" and val_b != "ALL":
            # Split by comma and clean whitespace
            set_a = set([x.strip() for x in val_a.split(',')])
            set_b = set([x.strip() for x in val_b.split(',')])
            
            print(f"  → {group_a_name} set: {set_a}")
            print(f"  → {group_b_name} set: {set_b}")
            
            if not set_a.issubset(set_b):
                print(f"  → {set_a} is NOT a subset of {set_b}")
                return False
            else:
                print(f"  → {set_a} IS a subset of {set_b}")
    
    return True

def check_all_subset_relationships(profiles_df):
    """
    Check all possible subset relationships between groups
    """
    groups = profiles_df.columns.tolist()
    print(f"Available groups: {groups}")
    print("\n" + "="*60)
    
    subset_relationships = []
    
    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                print(f"\n{'='*60}")
                is_subset = is_subset_group(group_a, group_b, profiles_df)
                if is_subset:
                    subset_relationships.append((group_a, group_b))
                    print(f"\n✅ RESULT: {group_a} IS a subset of {group_b}")
                else:
                    print(f"\n❌ RESULT: {group_a} is NOT a subset of {group_b}")
                print("="*60)
    
    return subset_relationships

def check_specific_subset(group_a_name, group_b_name, profiles_df):
    """
    Check if a specific group is a subset of another specific group
    """
    print(f"\nSPECIFIC SUBSET CHECK: Is {group_a_name} a subset of {group_b_name}?")
    print("="*60)
    
    is_subset = is_subset_group(group_a_name, group_b_name, profiles_df)
    
    if is_subset:
        print(f"\n✅ FINAL RESULT: {group_a_name} IS a subset of {group_b_name}")
    else:
        print(f"\n❌ FINAL RESULT: {group_a_name} is NOT a subset of {group_b_name}")
    
    return is_subset

if __name__ == "__main__":
    print("SUBSET RELATIONSHIP ANALYSIS")
    print("="*60)
    
    # Check all relationships
    subset_relationships = check_all_subset_relationships(Sample_Profiles)
    
    print(f"\n\nFINAL SUMMARY:")
    print("="*40)
    if subset_relationships:
        print("Found subset relationships:")
        for subset, parent in subset_relationships:
            print(f"  • {subset} is a subset of {parent}")
    else:
        print("No subset relationships found.")
    
    # Specific check: Group_3 vs Group_1 (as mentioned in your request)
    print("\n" + "="*60)
    check_specific_subset("Group_3", "Group_1", Sample_Profiles)
