import pandas as pd
import numpy as np

# Load the data for subset analysis
Sample_Profiles = pd.read_csv("artifacts/2025-05-31T13-26_export.csv").set_index('Unnamed: 0').fillna("ALL")

# Load the actual data for merging
profiles = pd.read_csv("artifacts/2025-05-31T13-27_export (1).csv").set_index('Unnamed: 0')
weights_for_profiles = pd.read_csv("artifacts/2025-05-31T13-27_export.csv").set_index('Unnamed: 0')

print("Sample Profiles DataFrame (for subset analysis):")
print(Sample_Profiles)
print("\n" + "="*50 + "\n")

print("Actual Profiles DataFrame (for merging):")
print(profiles)
print("\n" + "="*50 + "\n")

print("Weights DataFrame:")
print(weights_for_profiles.head(10))
print("\n" + "="*50 + "\n")

def is_subset_group(group_a_name, group_b_name, profiles_df):
    """
    Check if group_a is a subset of group_b

    Args:
        group_a_name: Name of the potential subset group
        group_b_name: Name of the potential parent group
        profiles_df: DataFrame with group profiles

    Returns:
        bool: True if group_a is a subset of group_b
    """
    if group_a_name not in profiles_df.columns or group_b_name not in profiles_df.columns:
        return False

    group_a = profiles_df[group_a_name]
    group_b = profiles_df[group_b_name]

    print(f"Checking if {group_a_name} is a subset of {group_b_name}:")
    print(f"{group_a_name} values: {group_a.to_dict()}")
    print(f"{group_b_name} values: {group_b.to_dict()}")

    # Check each attribute/index
    for attribute in profiles_df.index:
        val_a = str(group_a[attribute]).strip()
        val_b = str(group_b[attribute]).strip()

        print(f"\nAttribute '{attribute}':")
        print(f"  {group_a_name}: '{val_a}'")
        print(f"  {group_b_name}: '{val_b}'")

        # If group_b has "ALL", it contains everything, so group_a is subset
        if val_b == "ALL":
            print(f"  → {group_b_name} has 'ALL' for {attribute}, so {group_a_name} is subset")
            continue

        # If group_a has "ALL" but group_b doesn't, then group_a is NOT a subset
        if val_a == "ALL" and val_b != "ALL":
            print(f"  → {group_a_name} has 'ALL' but {group_b_name} doesn't, so NOT a subset")
            return False

        # If both have specific values, check if group_a values are contained in group_b
        if val_a != "ALL" and val_b != "ALL":
            # Split by comma and clean whitespace
            set_a = set([x.strip() for x in val_a.split(',')])
            set_b = set([x.strip() for x in val_b.split(',')])

            print(f"  → {group_a_name} set: {set_a}")
            print(f"  → {group_b_name} set: {set_b}")

            if not set_a.issubset(set_b):
                print(f"  → {set_a} is NOT a subset of {set_b}")
                return False
            else:
                print(f"  → {set_a} IS a subset of {set_b}")

    return True

def check_all_subset_relationships(profiles_df):
    """
    Check all possible subset relationships between groups
    """
    groups = profiles_df.columns.tolist()
    print(f"Available groups: {groups}")
    print("\n" + "="*60)

    subset_relationships = []

    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                print(f"\n{'='*60}")
                is_subset = is_subset_group(group_a, group_b, profiles_df)
                if is_subset:
                    subset_relationships.append((group_a, group_b))
                    print(f"\n✅ RESULT: {group_a} IS a subset of {group_b}")
                else:
                    print(f"\n❌ RESULT: {group_a} is NOT a subset of {group_b}")
                print("="*60)

    return subset_relationships

def check_specific_subset(group_a_name, group_b_name, profiles_df):
    """
    Check if a specific group is a subset of another specific group
    """
    print(f"\nSPECIFIC SUBSET CHECK: Is {group_a_name} a subset of {group_b_name}?")
    print("="*60)

    is_subset = is_subset_group(group_a_name, group_b_name, profiles_df)

    if is_subset:
        print(f"\n✅ FINAL RESULT: {group_a_name} IS a subset of {group_b_name}")
    else:
        print(f"\n❌ FINAL RESULT: {group_a_name} is NOT a subset of {group_b_name}")

    return is_subset

def merge_subset_groups(profiles_df, weights_df, subset_relationships):
    """
    Merge subset groups into their parent groups
    - Remove only subset groups (keep parent groups)
    - Average weights between subset and parent groups
    - Fill empty values in parent profiles from subset profiles
    """
    print(f"\n🔄 STARTING MERGE PROCESS...")
    print("="*60)

    if not subset_relationships:
        print("❌ No subset relationships to merge.")
        return profiles_df.copy(), weights_df.copy()

    # Create copies to work with
    merged_profiles = profiles_df.copy()
    merged_weights = weights_df.copy()

    # Track which groups to remove (only subset groups)
    groups_to_remove = set()

    print("📊 MERGING SUBSET GROUPS INTO PARENT GROUPS:")
    print("-"*60)

    for subset_group, parent_group in subset_relationships:
        if subset_group in groups_to_remove:
            print(f"⚠️  {subset_group} already processed, skipping...")
            continue

        print(f"\n🔹 Merging {subset_group} → {parent_group}:")

        # 1. Fill empty values in parent profile from subset profile
        print(f"   1. Filling empty values in {parent_group} profile...")
        filled_count = 0
        for attribute in merged_profiles.index:
            parent_val = merged_profiles.loc[attribute, parent_group]
            subset_val = merged_profiles.loc[attribute, subset_group]

            # Check if parent has empty/nan value but subset has a value
            parent_empty = pd.isna(parent_val) or str(parent_val).strip() in ['', 'nan', 'NaN']
            subset_empty = pd.isna(subset_val) or str(subset_val).strip() in ['', 'nan', 'NaN']

            if parent_empty and not subset_empty:
                merged_profiles.loc[attribute, parent_group] = subset_val
                print(f"      • {attribute}: filled '{subset_val}' from {subset_group}")
                filled_count += 1

        if filled_count == 0:
            print(f"      • No empty values to fill in {parent_group}")

        # 2. Merge weights by averaging
        print(f"   2. Averaging weights between {subset_group} and {parent_group}...")

        subset_weight_col = f"{subset_group}_Relative_Percentage"
        parent_weight_col = f"{parent_group}_Relative_Percentage"

        if subset_weight_col in merged_weights.columns and parent_weight_col in merged_weights.columns:
            weight_changes = 0
            for category in merged_weights.index:
                subset_weight = merged_weights.loc[category, subset_weight_col]
                parent_weight = merged_weights.loc[category, parent_weight_col]

                # Calculate average (handle NaN values)
                if pd.isna(subset_weight) and pd.isna(parent_weight):
                    avg_weight = np.nan
                elif pd.isna(subset_weight):
                    avg_weight = parent_weight
                elif pd.isna(parent_weight):
                    avg_weight = subset_weight
                else:
                    avg_weight = (subset_weight + parent_weight) / 2
                    if abs(avg_weight - parent_weight) > 0.01:  # Only count significant changes
                        weight_changes += 1
                        print(f"      • {category}: {parent_weight:.1f} + {subset_weight:.1f} → {avg_weight:.1f}")

                merged_weights.loc[category, parent_weight_col] = avg_weight

            if weight_changes == 0:
                print(f"      • No significant weight changes")
            else:
                print(f"      • Updated {weight_changes} weight values")

        # Mark ONLY the subset group for removal (keep parent)
        groups_to_remove.add(subset_group)
        print(f"   3. Marked {subset_group} for removal (keeping parent {parent_group})")

    # 3. Remove subset groups
    print(f"\n🗑️  REMOVING SUBSET GROUPS:")
    print("-"*40)

    for group_to_remove in groups_to_remove:
        # Remove from profiles
        if group_to_remove in merged_profiles.columns:
            merged_profiles = merged_profiles.drop(columns=[group_to_remove])
            print(f"   ✓ Removed {group_to_remove} from profiles")

        # Remove from weights
        weight_col_to_remove = f"{group_to_remove}_Relative_Percentage"
        if weight_col_to_remove in merged_weights.columns:
            merged_weights = merged_weights.drop(columns=[weight_col_to_remove])
            print(f"   ✓ Removed {weight_col_to_remove} from weights")

    return merged_profiles, merged_weights

def get_final_results():
    """
    Get the three final results from in-memory data:
    - Sample_Profiles_final: Independent groups only (subset groups removed), "ALL" replaced with ""
    - merged_weights: Weights with subset groups merged and averaged
    - merged_profiles: Profiles with subset groups merged and empty values filled

    Returns:
        tuple: (Sample_Profiles_final, merged_weights, merged_profiles)
    """
    # Find subset relationships using the loaded Sample_Profiles data
    subset_relationships = []
    groups = Sample_Profiles.columns.tolist()

    for i, group_a in enumerate(groups):
        for j, group_b in enumerate(groups):
            if i != j:  # Don't compare group with itself
                if is_subset_group(group_a, group_b, Sample_Profiles):
                    subset_relationships.append((group_a, group_b))

    # Get independent groups (not subsets of any other group)
    all_groups = Sample_Profiles.columns.tolist()
    subset_groups = set([rel[0] for rel in subset_relationships])
    independent_groups = [group for group in all_groups if group not in subset_groups]

    # Create Sample_Profiles_final with independent groups only, replace "ALL" with ""
    Sample_Profiles_final = Sample_Profiles[independent_groups].replace("ALL", "")

    # Perform merging on the loaded profiles and weights data
    merged_profiles, merged_weights = merge_subset_groups(profiles, weights_for_profiles, subset_relationships)

    return Sample_Profiles_final, merged_weights, merged_profiles

if __name__ == "__main__":
    print("SUBSET RELATIONSHIP ANALYSIS")
    print("="*60)

    # Check all relationships
    subset_relationships = check_all_subset_relationships(Sample_Profiles)

    print(f"\n\n🎯 COMPLETE SUBSET ANALYSIS RESULTS:")
    print("="*60)

    # Show all groups
    all_groups = Sample_Profiles.columns.tolist()
    print(f"📊 Available Groups: {all_groups}")
    print()

    if subset_relationships:
        print("✅ FOUND SUBSET RELATIONSHIPS:")
        print("-" * 40)
        for subset, parent in subset_relationships:
            print(f"  🔹 {subset} is a SUBSET of {parent}")
            print(f"     → This means {subset} can potentially be merged into {parent}")

        print(f"\n📈 Total subset relationships found: {len(subset_relationships)}")

        # Show which groups are NOT subsets of any other group
        subset_groups = set([rel[0] for rel in subset_relationships])
        independent_groups = [group for group in all_groups if group not in subset_groups]

        if independent_groups:
            print(f"\n🔸 Groups that are NOT subsets of any other group:")
            for group in independent_groups:
                print(f"     • {group}")
        print("Final Profiles selection")
        Sample_Profiles_final = Sample_Profiles[independent_groups].replace("ALL", "")
        print(Sample_Profiles_final)

        # Show which groups contain other groups
        parent_groups = set([rel[1] for rel in subset_relationships])
        if parent_groups:
            print(f"\n🔹 Groups that CONTAIN other groups:")
            for parent in parent_groups:
                subsets = [rel[0] for rel in subset_relationships if rel[1] == parent]
                print(f"     • {parent} contains: {', '.join(subsets)}")

    else:
        print("❌ NO SUBSET RELATIONSHIPS FOUND")
        print("All groups are independent - none can be merged based on subset logic")
        print("\nThis means each group has unique targeting criteria that don't overlap")

    # PERFORM MERGING BASED ON SUBSET RELATIONSHIPS
    print(f"\n\n{'='*80}")
    print("🔄 MERGING SUBSET GROUPS")
    print("="*80)

    if subset_relationships:
        # Perform the merge using the actual data files
        merged_profiles, merged_weights = merge_subset_groups(profiles, weights_for_profiles, subset_relationships)

        # Show results
        print(f"\n📋 MERGE RESULTS:")
        print("="*60)
        print("ORIGINAL PROFILES:")
        print(profiles)
        print(f"\nMERGED PROFILES:")
        print(merged_profiles)

        print(f"\nORIGINAL WEIGHTS (first 10 rows):")
        print(weights_for_profiles.head(10))
        print(f"\nMERGED WEIGHTS (first 10 rows):")
        print(merged_weights.head(10))

        # Save results
        merged_profiles.to_csv("subset_merged_profiles.csv")
        merged_weights.to_csv("subset_merged_weights.csv")

        print(f"\n💾 RESULTS SAVED:")
        print(f"   • subset_merged_profiles.csv")
        print(f"   • subset_merged_weights.csv")

        print(f"\n🎉 FINAL SUMMARY:")
        print("="*60)
        print(f"   • Original groups: {len(profiles.columns)} → Final groups: {len(merged_profiles.columns)}")
        print(f"   • Groups removed: {set(profiles.columns) - set(merged_profiles.columns)}")
        print(f"   • Groups remaining: {list(merged_profiles.columns)}")

    else:
        print("No merging performed - no subset relationships found.")

    # DEMONSTRATE THE FINAL FUNCTION
    print(f"\n\n{'='*80}")
    print("🎯 FINAL FUNCTION RESULTS")
    print("="*80)

    # Get the three final results using the function
    Sample_Profiles_final, merged_weights, merged_profiles = get_final_results()

    print("✅ Sample_Profiles_final:")
    print(Sample_Profiles_final)
    print("\n✅ merged_profiles:")
    print(merged_profiles)
    print("\n✅ merged_weights (first 10 rows):")
    print(merged_weights.head(10))

    print(f"\n🎉 Function returns exactly these three results!")
    print("Usage: Sample_Profiles_final, merged_weights, merged_profiles = get_final_results()")
