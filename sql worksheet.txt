username - TeamAffixcon
password - IDnoCdemojem2N
UTJJBCP-PN17465.snowflakecomputing.com
UTJJBCP.PN17465
Account Locator  KS99141
PN17465

Account details
Account Name TESTACCOUNT
Account URL https://yorquxj-testaccount.snowflakecomputing.com
Account Locator IZ40551
Account Locator URL https://iz40551.ap-southeast-2.snowflakecomputing.com
Edition Standard
Cloud Amazon Web Services
Region Asia Pacific (Sydney)
Admin login
Admin User Name TestAccount
Admin <NAME_EMAIL>


use role accountadmin;
grant create application package on account to role accountadmin;
create application package hello_snowflake_package;
show application packages;

use application package hello_snowflake_package;
create schema stage_content

CREATE OR REPLACE STAGE hello_snowflake_package.stage_content.hello_snowflake_stage
    FILE_FORMAT = (TYPE = 'CSV' FIELD_DELIMITER = ',' SKIP_HEADER = 1);

list @hello_snowflake_package.stage_content.hello_snowflake_stage;

create application hello_snowflake_app
    from application package HELLO_SNOWFLAKE_PACKAGE
    using '@hello_snowflake_package.stage_content.hello_snowflake_stage';

show applications;

call core.hello();

USE APPLICATION PACKAGE hello_snowflake_package;

CREATE SCHEMA IF NOT EXISTS shared_data;
USE SCHEMA shared_data;
CREATE TABLE IF NOT EXISTS accounts (ID INT, NAME VARCHAR, VALUE VARCHAR);
TRUNCATE TABLE accounts;
INSERT INTO accounts VALUES
  (1, 'Joe', 'Snowflake'),
  (2, 'Nima', 'Snowflake'),
  (3, 'Sally', 'Snowflake'),
  (4, 'Juan', 'Acme');
-- grant usage on the ``ACCOUNTS`` table
GRANT USAGE ON SCHEMA shared_data TO SHARE IN APPLICATION PACKAGE hello_snowflake_package;
GRANT SELECT ON TABLE accounts TO SHARE IN APPLICATION PACKAGE hello_snowflake_package;

select * from accounts;
drop application HELLO_SNOWFLAKE_APP;

create application hello_snowflake_app
    from application package HELLO_SNOWFLAKE_PACKAGE
    using '@hello_snowflake_package.stage_content.hello_snowflake_stage';


select * from code_schema.accounts_view;

drop application HELLO_SNOWFLAKE_APP;

create application hello_snowflake_app
    from application package HELLO_SNOWFLAKE_PACKAGE
    using '@hello_snowflake_package.stage_content.hello_snowflake_stage';


alter application package HELLO_SNOWFLAKE_PACKAGE
add version v1_0 using '@hello_snowflake_package.stage_content.hello_snowflake_stage';

show versions in application package HELLO_SNOWFLAKE_PACKAGE;

drop application HELLO_SNOWFLAKE_APP;

create application hello_snowflake_app
    from application package HELLO_SNOWFLAKE_PACKAGE
    using version v1_0;

alter application package HELLO_SNOWFLAKE_PACKAGE
set default release directive 
version = v1_0
patch = 0;



PUT file://streamlit/data_enrich.py  @hello_snowflake_package.stage_content.hello_snowflake_stage/streamlit AUTO_COMPRESS = FALSE    OVERWRITE = TRUE;