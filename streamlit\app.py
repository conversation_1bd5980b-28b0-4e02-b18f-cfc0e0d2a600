import streamlit as st
import pandas as pd
import numpy as np
import datetime
import ast
# from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import LabelEncoder, StandardScaler
from utils import *
from profile_extraction import *
from snowflake.snowpark import Session
from data_enrich import enrich_data


# # # Set page configuration
# st.set_page_config(
#     page_title="SimilarSeek",
#     page_icon="📊",
#     layout="wide"
# )
# App title and description
st.title("SimilarSeek")
st.markdown("""
Lookalike modeling uses your customer data to find new customers similar to your best ones.\n

""")

st.markdown("Industry selected – :green[Charity]")
data = st.file_uploader("Upload your customer data file", type=["csv", "xlsx"])
if data is not None:
    if data.name.endswith('.csv'):
        data = pd.read_csv(data,encoding='latin1', nrows=100).fillna('')
    elif data.name.endswith('.xlsx'):
        # If the file is an Excel file, read the first sheet
        data = pd.read_excel(data, sheet_name=0, encoding='latin1').fillna('')
    else:
        st.error("Unsupported file format. Please upload a CSV or Excel file.")
        st.stop()
    with st.expander("Data Preview"):
        st.dataframe(data.head(3),use_container_width=True)

    session = Session.builder.getOrCreate()
    bsm_concat = session.sql("select * from code_schema.bsm_view_all;").to_pandas()

    st.markdown("""
    This application demonstrates how to enrich customer data with missing Age, Gender, Income, and HomeOwner values
    by matching records with a reference dataset using multiple levels of matching criteria.

    1. **Level 1**: FullName + DPID
    2. **Level 2**: DPID + Mobile
    3. **Level 3**: DPID + Email
    4. **Level 4**: Mobile
    5. **Level 5**: Email
    6. **Level 6**: DPID
    7. **Level 7**: Address + Postcode
    """)

    with st.spinner("Enriching data..."):
        # Enrich the data
        enriched_data = enrich_data(data, bsm_concat)

    # # Display enriched data
    # st.header("Enriched Data")
    # st.dataframe(enriched_data)

    # Display statistics
    st.header("Enrichment Statistics")

    # Calculate statistics for each field
    stats = []
    total_records = len(enriched_data)

    def filled_count(col, verified_col=None):
        filled = (enriched_data[col] != '').sum() + enriched_data[col].notna().sum() - (enriched_data[col] == '').sum()
        if verified_col and verified_col in enriched_data.columns:
            filled_verified = (enriched_data[verified_col] != '').sum()
            return filled, filled_verified
        return filled, None

    fields = [
        ("Age", "Age_Verified"),
        ("Gender", "Gender_Verified"),
        ("Income", "Income_Verified"),
        ("HomeOwner", "HomeOwner_Verified"),
        ("Phone2_Mobile", "Mobile_Verified"),
        ("EmailAddress", "EmailAddress_Verified"),
    ]

    for col, verified_col in fields:
        base_filled = (enriched_data[col] != '').sum()
        enriched_filled = (enriched_data[verified_col] != '').sum() if col in data.columns else 0
        percent = (enriched_filled / total_records * 100) if total_records else 0
        # increment = enriched_filled - base_filled
        # increment_percent = ((increment / base_filled) * 100) if base_filled else 0
        stats.append({
            "Field": col.replace("Phone2_Mobile", "Mobile").replace("EmailAddress", "Email"),
            "Total Records": total_records,
            "Filled Before": base_filled if col in data.columns else 0,
            "Filled After": (enriched_data[verified_col] != '').sum(),
            # "Enrichment (%)": f"{percent:.1f}%"
            "Enrichment (%)": f"{(enriched_data[verified_col] != '').sum() / total_records * 100:.1f}%"
        })

    stats_df = pd.DataFrame(stats)
    st.dataframe(stats_df.set_index("Field"), use_container_width=True)
        # st.write(f"Missing Email values: {email_missing}")
    # Download option for enriched data
    # csv = enriched_data.to_csv(index=False)
    # st.download_button(
    #     label="Download Enriched Data as CSV",
    #     data=csv,
    #     file_name="enriched_data.csv",
    #     mime="text/csv",
    # )
    # st.markdown("---enrichment completed---")
    # st.dataframe(enriched_data, use_container_width=True)
    population_filter_demog, weights_for_profiles,profiles = lookalike_profile_extraction(enriched_data)

    population_filter_demog = population_filter_demog.replace("","ALL")
  
    # profiles = profiles.replace({}, np.nan)
    # profiles = profiles.replace('',np.nan)

    use_index = [col for col in ['Income', 'HomeOwner', 'Gender', 'Age','Group_percentage'] if col in profiles.index]
    
    population_filter_demog = population_filter_demog.loc[use_index]
    profiles = profiles.loc[use_index]
    st.write("Sample Profiles:")
    st.dataframe( population_filter_demog, use_container_width=True)
    st.write("weights_for_profiles", weights_for_profiles)
    st.write("profiles", profiles)

    with st.spinner("Extracting lookalike profiles..."):
        row_unique_values_dict = {
            index: list(set(row.dropna().values)) for index, row in profiles.iterrows()
        }
        row_unique_values_dict = {
            key: [item.strip() for sublist in value for item in sublist.split(', ')] 
            for key, value in row_unique_values_dict.items()
        }
        row_unique_values_dict = {
            key: list(set(value)) for key, value in row_unique_values_dict.items()
        }
        row_unique_values_dict = {
            key: [item for item in value if item.strip()] for key, value in row_unique_values_dict.items()
        }
        population_filter_demog = {key: value for key, value in row_unique_values_dict.items() if key in ['Age', 'Gender', 'HomeOwner', 'Income', 'Group_percentage']}
        population = bsm_concat[
        (bsm_concat['Age1'.upper()].isin(population_filter_demog['Age'])) &
        (bsm_concat['Gender1'.upper()].isin(population_filter_demog['Gender'])) &
        (bsm_concat['HomeOwner1'.upper()].isin(population_filter_demog['HomeOwner'])) &
        (bsm_concat['Income1'.upper()].isin(population_filter_demog['Income']))
        ]
        # st.write("Population size:", population.shape[0])
        # st.write("Population preview:")
        # st.dataframe(population.head(3), use_container_width=True)


        #-------------------------
        result = {
        col: weights_for_profiles[col].to_dict()
        for col in weights_for_profiles.drop(columns=["Category"]).columns
        }
        result = {
            key.replace('_Relative_Percentage', ''): value
            for key, value in result.items()
        }
        # st.write("Weights for profiles:", result)


        for group in result.keys():  # Iterate over Group_1, Group_2, etc.
            population[group] = population.apply(
                lambda row: sum(
                    result[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
                ),
                axis=1
            )

        
        group_columns = [col for col in population.columns if col.startswith('Group_')]
        population['max_value'] = population[group_columns].max(axis=1)
        population['selected_group'] = population[group_columns].idxmax(axis=1)


        from sklearn.preprocessing import MinMaxScaler

        # Initialize the MinMaxScaler
        scaler = MinMaxScaler()

        # Select the columns to normalize
        columns_to_normalize = [
            col for col in weights_for_profiles.columns if 'Relative_Percentage' in col
        ]

        # Group by 'Category' and apply MinMaxScaler to each group
        weights_for_profiles[[f'normalized_{col}' for col in columns_to_normalize]] = (
            weights_for_profiles[columns_to_normalize]
            .div(weights_for_profiles[columns_to_normalize].sum(axis=1), axis=0)
        )

        result1 = {
        col: weights_for_profiles[col].to_dict()
        for col in weights_for_profiles.drop(columns=["Category"]).columns
        }
        result1 = {
            key.replace('_Relative_Percentage', ''): value
            for key, value in result1.items()
        }
        result1 = {
            key.replace('normalized_', ''): value
            for key, value in result1.items()
        }

        for group in result1.keys():  # Iterate over Group_1, Group_2, etc.
            population[group] = population.apply(
                lambda row: sum(
                    result1[group].get(row[col], 0) for col in ['Age1'.upper(), 'Gender1'.upper(), 'Income1'.upper(), 'HomeOwner1'.upper()]
                ),
                axis=1
            )

        
        population['max_value'] = population[group_columns].max(axis=1)
        population['selected_group'] = population[group_columns].idxmax(axis=1)

        #----------
        population['GIFT_CASUSE_PREFENCE_Flag'] = population['GIFT_CASUSE_PREFENCE'].apply(lambda x: 1 if x != '' else 0)
        bsm_segments_list = [
            'Segment_8DD_Festivals',
            'Segment_Facebook_Music',
            'Segment_Facebook_Music_Fantastic',
            'Travel_Domestic',
            'Travel_International',
            'Segment_8DD_Travel_Domestic_Air',
            'Segment_8DD_Travel_Domestic_Rail',
            'Segment_8DD_football_spectator',
            'Segment_8DD_tennis_spectator',
            'Segment_8DD_rugby_spectator',
            'Segment_8DD_Debit_Card',
            'Segment_8DD_Invest_Crypto',
            'Segment_8DD_Invest_Shares',
            'charity_donor_online',
            'charity_donor_facetoface'
        ]
        bsm_segments_list = [segment.upper() for segment in bsm_segments_list]

        # df2['BSM_segment_Flag'] = df2[bsm_segments_list].apply(lambda row: row.eq('Y').any(), axis=1).astype(int)
        population['BSM_segments_Flag'] = population[bsm_segments_list].apply(lambda row: row.eq('Y').sum(), axis=1)

        bsm_donor_involved = ['Charity_6Months',
        'Charity_12Months',
        'Charity_24Months',
        'Charity_24Months_More']
        bsm_donor_involved = [segment.upper() for segment in bsm_donor_involved]

        population['BSM_doner_involved_Flag'] = population[bsm_donor_involved].apply(lambda row: row.eq(1).sum(), axis=1)

        population['BSM_total_score'] = population['BSM_segments_Flag']*0.1 + population['BSM_doner_involved_Flag']*0.3 + population['max_value']*0.6

        # st.write("Population with BSM segments flag:")
        # st.dataframe(population.head(3), use_container_width=True)

    #-------------------------
        digi_merge = session.sql("select * from code_schema.digi_merge_view;").to_pandas()
        population['POSTCODE'] = population['POSTCODE'].astype(str)
        # digi_merge['postcode'] = digi_merge['postcode'].astype(str)
        digi_merge['POSTCODE'] = digi_merge['POSTCODE'].astype(str).str.replace('.0', '', regex=False)
        digi_merge['address_key'] = digi_merge[['AD1', 'SUBURB', 'STATE', 'POSTCODE']].astype(str).agg('|'.join, axis=1)
        digi_merge_sorted = digi_merge.sort_values(
            by=['address_key', 'full_score'.upper()],
            ascending=[True, False]
        )
        digi_merge_sorted = digi_merge_sorted.drop_duplicates(subset='address_key', keep='first')
        merged_df = population.merge(
            digi_merge_sorted[['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper(), 'full_score'.upper()]],
            left_on=['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper()],
            right_on=['ad1'.upper(), 'suburb'.upper(), 'state'.upper(), 'postcode'.upper()],
            how='left',
            indicator=True
        )
        merged_df['full_score'.upper()] = merged_df['full_score'.upper()].fillna(merged_df['BSM_total_score'])
        merged_df['Final_score'.upper()] = merged_df['BSM_total_score'] + merged_df['full_score'.upper()]
        merged_df_sorted = merged_df.sort_values('Final_score'.upper(), ascending=False)
        # st.write("Merged DataFrame with final scores:")
        # st.dataframe(merged_df_sorted.head(3), use_container_width=True)


    #-------------------------
        st.markdown(f"Total records:- :green[{len(merged_df_sorted)}]")
        no_of_records =st.number_input("No of records to filter", min_value=1, max_value=len(merged_df_sorted), value=10000, step=100, help="Number of records to filter based on the selected percentage")
        st.text("Higher scores reflect stronger alignment with your current customer profile, while lower scores indicate weaker similarity")
        # top_25_percent = merged_df_sorted.iloc[:int(len(merged_df_sorted) * top_percentage/100)]
        
        # Checkbox to exclude records present in enriched_data from merged_df_sorted
        exclude_existing = st.checkbox("Exclude records already in your uploaded data", value=True)
        filtered_merged_df_sorted = merged_df_sorted.copy()
        if exclude_existing:
            filtered_merged_df_sorted = filtered_merged_df_sorted[~filtered_merged_df_sorted['SOURCE_URN'].isin(enriched_data['SOURCE_URN'])]

        top_25_percent = filtered_merged_df_sorted.iloc[:no_of_records]
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['EmailAddress'.upper()] = top_25_percent['EmailAddress'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if isinstance(x, str) and len(x) > 0 else x)

        # Filter rows where DSAVerified is 'Yes' and sort by Final_score in descending order
        filtered_sorted_df = top_25_percent.sort_values(
            by=['DSAVerified'.upper(), 'Final_score'.upper()], 
            ascending=[False, False],
            key=lambda col: col.fillna('') if col.name == 'DSAVerified'.upper() else col
        )

        # st.dataframe(filtered_sorted_df[['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper(), 'Phone1_Landline'.upper(), 'Phone2_Mobile'.upper(),
                # 'EmailAddress'.upper(), 'selected_group', 'DSAVerified'.upper(), 'Final_score'.upper()]].head(10).reset_index(drop=True),use_container_width=True)
        top_25_percent = merged_df_sorted.iloc[:no_of_records]
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone1_Landline'.upper()] = top_25_percent['Phone1_Landline'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].astype(str).str.replace('.0', '', regex=False)
        top_25_percent['Phone2_Mobile'.upper()] = top_25_percent['Phone2_Mobile'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if x.isdigit() else x)

        top_25_percent['EmailAddress'.upper()] = top_25_percent['EmailAddress'.upper()].apply(lambda x: x[0] + '#' + '*' * 5 if isinstance(x, str) and len(x) > 0 else x)

        # Filter rows where DSAVerified is 'Yes' and sort by Final_score in descending order
        filtered_sorted_df = top_25_percent.sort_values(
            by=['DSAVerified'.upper(), 'Final_score'.upper()], 
            ascending=[False, False],
            key=lambda col: col.fillna('') if col.name == 'DSAVerified'.upper() else col
        )


        # Write the filtered and sorted DataFrame to a CSV file
        # filtered_sorted_df.to_csv('filtered_sorted_output.csv', index=False)

        st.dataframe(filtered_sorted_df[['Ad1'.upper(), 'Suburb'.upper(), 'State'.upper(), 'Postcode'.upper(), 'Phone1_Landline'.upper(), 'Phone2_Mobile'.upper(),
                'EmailAddress'.upper(), 'selected_group', 'DSAVerified'.upper(), 'Final_score'.upper()]].head(10).reset_index(drop=True),use_container_width=True)