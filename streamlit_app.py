import streamlit as st
import pandas as pd
# import snowflake.snowpark as snowpark
from snowflake.snowpark.context import get_active_session

def main():
    st.title("🔍 Lookalike Audience Enrichment")
    
    # session = snowpark.Session.builder.getOrCreate()
    session = get_active_session()
    
    df = session.table("profiling_db.public.all_bsm_data").to_pandas()
    
    st.write("Sample Uploaded Data", df.head())

    enriched_df = df.copy()
    enriched_df["Gender"] = enriched_df["Gender"].replace({"M": "Male", "F": "Female"})

    st.write("🔧 Enriched Data Preview")
    st.dataframe(enriched_df)

if __name__ == "__main__":
    main()
