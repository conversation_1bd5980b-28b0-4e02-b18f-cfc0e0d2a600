import pandas as pd
import numpy as np
from datetime import datetime
import re

def age(dob):
    born_year = pd.to_datetime(dob).date().year
    current_year = datetime.datetime.now().year
    age = current_year - born_year
    return age

def preprocss(df):
    
    #------------------------------Age-------------------------------------------
    # age_df = df[['Age',"Merged_Age_Range","Age_Merged","Age_S4614","Age_Range_S4614","DOB_Formatted","DOB_Merged"]]
    age_df = df[["Merged_Age_Range","DOB_Formatted","DOB_Merged"]].copy()
    age_df['AgeNew'] = age_df['DOB_Formatted'].apply(age)
    age_ranges = {
    "0-4": (0, 4),
    "5-9": (5, 9),
    "10-14": (10, 14),
    "15-19": (15, 19),
    "20-24": (20, 24),
    "25-29": (25, 29),
    "30-34": (30, 34),
    "35-39": (35, 39),
    "40-44": (40, 44),
    "45-49": (45, 49),
    "50-54": (50, 54),
    "55-59": (55, 59),
    "60-64": (60, 64),
    "65-69": (65, 69),
    "70-74": (70, 74),
    "75-79": (75, 79),
    "80-84": (80, 84),
    "85-89": (85, 89),
    "90-94": (90, 94),
    "Over 95": (95, float('inf'))}
    def get_age_range(age):
        for label, (lower, upper) in age_ranges.items():
            if lower <= age <= upper:
                return label
        return "" 
    
    age_df.loc[
        (age_df['DOB_Formatted'].isna()) & (age_df['DOB_Merged'].notna()),
        'AgeNew'
    ] = age_df.loc[
        (age_df['DOB_Formatted'].isna()) & (age_df['DOB_Merged'].notna()),
        'DOB_Merged'
    ].apply(age)

    age_df['AgeRangeNew'] = age_df['AgeNew'].apply(get_age_range)

    age_df.loc[
        (age_df['AgeRangeNew'] == 'Unknown') & (age_df['Merged_Age_Range'].notna()),
        'AgeRangeNew'
    ] = age_df['Merged_Age_Range']
    # age_df.loc[
    #     (age_df['AgeRangeNew'] == 'Unknown') & (age_df['Age_Range_S4614'].notna()),
    #     'AgeRangeNew'
    # ] = age_df['Merged_Age_Range']
    

    
    #------------------------------Gender-------------------------------------------
    # gender_df = df[["FirstName","MiddleName","Surname",'Gender', "GenderTemp"]]
    gender_df = df[["GenderTemp"]]
    # gender_df['GenderNew'] = np.nan
    # gender_df.loc[
    #     ((gender_df['Gender'].notna()) & (gender_df['Gender'] != "_")),
    #     'GenderNew'
    # ] = gender_df['GenderTemp']

    # gender_df.loc[
    #     ((gender_df['GenderTemp'].notna()) & (gender_df['GenderTemp'] != "_")),
    #     'GenderNew'
    # ] = gender_df['Gender']

    # # gender_df['GenderNew'] = gender_df.apply(predict_gender, axis=1)
    # gender_df['GenderNew'] = gender_df['GenderNew'].fillna(gender_df['GenderTemp'])
    gender_df['GenderNew'] = gender_df['GenderTemp'].replace({'F':'female', 'M':'male'})

    #------------------------------HomeOwner-------------------------------------------
    home_owner_df = df[["HomeOwner","Homeowner_Merged"]]
    home_owner_df['HomeOwnerNew'] = np.nan

    home_owner_df.loc[
        (home_owner_df['HomeOwner'].notna()),
        'HomeOwnerNew'
    ] = home_owner_df['HomeOwner']

    home_owner_df.loc[
        ((home_owner_df['Homeowner_Merged'].notna())),
        'HomeOwnerNew'
    ] = home_owner_df['Homeowner_Merged']

    home_owner_df['HomeOwnerNew'] = home_owner_df['HomeOwnerNew'].fillna("renters")
    home_owner_df['HomeOwnerNew'] = home_owner_df['HomeOwnerNew'].replace({'Y':"HomeOwner"})
    
    # if is_toggled:
    #     analysis_df = pd.concat(
    #         [
    #             age_df[['AgeRangeNew']], 
    #             gender_df[['GenderNew']], 
    #             df[['IncomeRange_S4614']], 
    #             home_owner_df[['HomeOwnerNew']], 
    #             df[['DZ_Profile']],
    #             df[addition_features]
    #         ], 
    #         axis=1
    #     )
    # else:
    analysis_df = pd.concat(
        [
            age_df[['AgeRangeNew']], 
            gender_df[['GenderNew']], 
            df[['Income']], 
            home_owner_df[['HomeOwnerNew']]
        ], 
        axis=1
    )

    # analysis_df = analysis_df.fillna("")
    analysis_df = analysis_df.rename(columns={'AgeRangeNew':'Age1','GenderNew':'Gender1', 'HomeOwnerNew':'HomeOwner1', 'Income':'Income1'})

    return analysis_df